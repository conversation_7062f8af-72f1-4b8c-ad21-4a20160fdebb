import sys
import os
import pandas as pd
import traceback

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
sys.path.append(current_dir)

from trade.log.YfyLog import logger
from trade.manager.OneClickTradeMgr import OneClickTradeMgr
import trade.network.YFYAccountMgr as account_mgr
import trade.network.YFYDataMgr as data_mgr
from trade.redis.StockRedisMgr import StockRedisMgr, RedisKeyPrefix
from trade.strategy.StrategiesConfig import get_account_status
from trade.manager.emaStockTrade.ema_redis_config_manager import RedisConfigManager
from trade.strategy import StrategiesConfig as config


def safe_get_stock_name(symbol):
    """安全获取股票名称，如果找不到股票信息则尝试查找ETF信息"""
    try:
        # 首先尝试获取股票信息
        stock_info = data_mgr.get_stock_simple_info(symbol)
        if stock_info is not None and not stock_info.empty and "name" in stock_info.columns:
            # 使用 .iat[0, stock_info.columns.get_loc('name')] 来安全访问
            name_col_idx = stock_info.columns.get_loc('name')
            return str(stock_info.iat[0, name_col_idx])
        
        # 如果找不到股票信息，特别是1开头和5开头的代码，尝试查找ETF信息
        if symbol.startswith('1') or symbol.startswith('5'):
            logger.info(f"股票信息未找到，尝试查找ETF信息: {symbol}")
            etf_info = data_mgr.get_etf_simple_info(symbol)
            if etf_info is not None and not etf_info.empty and "name" in etf_info.columns:
                name_col_idx = etf_info.columns.get_loc('name')
                etf_name = str(etf_info.iat[0, name_col_idx])
                logger.info(f"在ETF中找到: {symbol} -> {etf_name}")
                return etf_name
            
    except Exception as e:
        logger.warning(f"获取股票/ETF名称失败 {symbol}: {e}")
    
    return symbol


class StockOneClickTradeService:
    """一键交易服务类 - 为Web界面提供交易接口"""
    
    _instance = None
    _initialized = False

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            logger.info(f"{cls.__name__}: Creating new singleton instance")
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if StockOneClickTradeService._initialized:
            return

        logger.info(f"{self.__class__.__name__}: Initializing singleton for the first time.")
        self.trade_mgr = OneClickTradeMgr()
        self.redis_config_mgr = RedisConfigManager()  # 初始化Redis配置管理器
        StockOneClickTradeService._initialized = True

    def update(self):
        """更新服务状态"""
        pass

    def get_account_list(self):
        """获取所有账户列表
        
        Returns:
            list: 账户类型列表
        """
        try:
            # 更新账户信息
            account_mgr.get_all_account_info()
            return list(self.trade_mgr.account_types)
        except Exception as e:
            logger.error(f"Error getting account list: {str(e)}")
            return []

    def get_account_info(self, account_type=None):
        """获取账户信息
        
        Args:
            account_type: 指定账户类型，如果为None则返回所有账户信息
            
        Returns:
            dict: 账户信息
        """
        try:
            # 更新账户信息
            account_mgr.get_all_account_info()
            
            if account_type:
                # 获取指定账户信息
                account_info = account_mgr.get_account_datainfo(account_type)
                if not account_info:
                    return {"status": "error", "message": f"无法获取账户 {account_type} 信息"}
                
                logger.info(f"账户 {account_type} 原始数据: {account_info}")
                
                # 检查账户信息类型和结构
                if isinstance(account_info, list):
                    if len(account_info) > 0:
                        account_info = account_info[0]
                    else:
                        return {"status": "error", "message": f"账户 {account_type} 数据为空"}
                
                if not isinstance(account_info, dict):
                    return {"status": "error", "message": f"账户 {account_type} 数据格式错误"}
                
                # 处理持仓信息
                positions = []
                if "positionInfoGroup" in account_info and account_info["positionInfoGroup"]:
                    for pos in account_info["positionInfoGroup"]:
                        if not isinstance(pos, dict):
                            continue
                        symbol = pos.get("symbol", "")
                        # 获取股票名称
                        stock_name = safe_get_stock_name(symbol)
                        
                        # 计算盈亏：市值 - 成本
                        trade_price = float(pos.get("tradePrice", 0))
                        trade_amount = int(pos.get("tradeAmount", 0))
                        position_gold = float(pos.get("positionGold", 0))
                        cost = trade_price * trade_amount
                        profit = position_gold - cost
                        profit_percent = (profit / cost * 100) if cost > 0 else 0
                        
                        # 获取当日盈亏数据
                        today_profit = float(pos.get("todayProfit", 0))
                        today_profit_percent = float(pos.get("todayProfitPercent", 0))
                        
                        positions.append({
                            "symbol": symbol,
                            "name": stock_name,
                            "quantity": trade_amount,
                            "availableAmount": int(pos.get("availableAmount", 0)),
                            "marketValue": position_gold,
                            "cost": cost,
                            "tradePrice": trade_price,
                            "latestPrice": float(pos.get("latestPrice", 0)),
                            "profit": profit,
                            "profitPercent": profit_percent,
                            "todayProfit": today_profit,
                            "todayProfitPercent": today_profit_percent,
                            "changePercent": float(pos.get("changePercent", 0)),
                            "boardName": pos.get("boardName", ""),
                            "conceptName": pos.get("conceptName", ""),
                            "tradeBuyTime": pos.get("tradeBuyTime", ""),
                            "strategyInfo": pos.get("strategyInfo", "")
                        })
                
                # 获取账户信息（totalCash字段就是总资产）
                total_cash = float(account_info.get("totalCash", 0))
                market_value = float(account_info.get("marketValue", 0))
                total_profit = float(account_info.get("totalProfit", 0))
                total_today_profit = float(account_info.get("totalTodayProfit", 0))
                
                return {
                    "status": "success",
                    "account_type": account_type,
                    "totalCash": total_cash,
                    "availableCash": float(account_info.get("availableCash", 0)),
                    "marketValue": market_value,
                    "totalProfit": total_profit,
                    "totalTodayProfit": total_today_profit,
                    "positions": positions
                }
            else:
                # 获取所有账户概览信息
                all_accounts = {}
                for acc_type in self.trade_mgr.account_types:
                    account_info = account_mgr.get_account_datainfo(acc_type)
                    if account_info:
                        # 检查账户信息类型和结构
                        if isinstance(account_info, list):
                            if len(account_info) > 0:
                                account_info = account_info[0]
                            else:
                                continue
                        
                        if not isinstance(account_info, dict):
                            continue
                        
                        position_count = 0
                        if "positionInfoGroup" in account_info and account_info["positionInfoGroup"]:
                            position_count = len(account_info["positionInfoGroup"])
                        
                        # 获取账户信息（totalCash字段就是总资产）
                        total_cash = float(account_info.get("totalCash", 0))
                        market_value = float(account_info.get("marketValue", 0))
                        total_profit = float(account_info.get("totalProfit", 0))
                        total_today_profit = float(account_info.get("totalTodayProfit", 0))  # 添加当日盈亏
                        
                        # 获取账户状态
                        account_status = get_account_status(acc_type)
                        
                        all_accounts[acc_type] = {
                            "totalCash": total_cash,
                            "availableCash": float(account_info.get("availableCash", 0)),
                            "marketValue": market_value,
                            "totalProfit": total_profit,
                            "totalTodayProfit": total_today_profit,  # 添加当日盈亏字段
                            "positionCount": position_count,
                            "status": account_status
                        }
                
                return {"status": "success", "accounts": all_accounts}
                
        except Exception as e:
            logger.error(f"Error getting account info: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    def clear_single_account(self, account_type, msg="一键清仓指定账户", exclude_symbols=None):
        """清仓单个账户，先关闭账户再执行清仓
        
        Args:
            account_type: 账户类型
            msg: 交易备注信息
            exclude_symbols: 要排除的股票代码列表，这些股票不会被清仓
        """
        try:
            # 第一步：先关闭账户，防止在清仓过程中接收新的交易指令
            logger.info(f"开始清仓账户 {account_type}，先关闭账户")
            self.toggle_account_status(account_type, 'close')
            
            # 第二步：执行清仓操作
            result = self.trade_mgr.sell_all_positions_for_account(account_type, msg, exclude_symbols)
            
            logger.info(f"清仓账户 {account_type} 结果: {result}")
                
            return result
        except Exception as e:
            logger.error(f"Error clearing single account {account_type}: {str(e)}")
            return {"status": "error", "message": str(e)}

    def clear_all_accounts(self, msg="一键清仓所有账户", exclude_symbols=None):
        """清仓所有账户，先关闭所有账户再执行清仓
        
        Args:
            msg: 交易备注信息
            exclude_symbols: 要排除的股票代码列表，这些股票不会被清仓
        """
        try:
            # 第一步：先关闭所有账户，防止在清仓过程中接收新的交易指令
            logger.info("开始清仓所有账户，先关闭所有账户")
            for acc_type in self.trade_mgr.account_types:
                self.toggle_account_status(acc_type, 'close')
            
            # 第二步：执行清仓操作
            result = self.trade_mgr.sell_all_account_positions(msg, exclude_symbols)
                
            logger.info(f"清仓所有账户结果: {result}")

            return result
        except Exception as e:
            logger.error(f"Error clearing all accounts: {str(e)}")
            return {"status": "error", "message": str(e)}

    def sell_stock_all_accounts(self, symbol, pos_percent, msg="一键卖出"):
        """卖出所有账户的指定股票
        
        Args:
            symbol: 股票代码
            pos_percent: 卖出比例(0-1)
            msg: 交易备注
            
        Returns:
            dict: 交易结果
        """
        try:
            # 参数验证
            if not symbol:
                return {"status": "error", "message": "股票代码不能为空"}
            
            if pos_percent <= 0 or pos_percent > 1:
                return {"status": "error", "message": "卖出比例必须在0-1之间"}
            
            logger.info(f"开始执行一键卖出: {symbol}, 比例: {pos_percent}")
            result = self.trade_mgr.sell_stock_for_all_account(symbol, pos_percent, msg)
            logger.info(f"卖出股票 {symbol} ({pos_percent*100}%) 结果: {result}")

            # 如果是100%卖出，则禁用该股票或ETF的EMA策略
            if pos_percent == 1:
                self._disable_ema_strategy(symbol)
                
            return result
        except Exception as e:
            logger.error(f"Error selling stock {symbol}: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    def _disable_ema_strategy(self, symbol):
        """禁用指定股票或ETF的EMA策略"""
        try:
            # 尝试作为股票处理
            stock_config = self.redis_config_mgr.get_stock_config(symbol)
            if stock_config:
                if stock_config.get("enabled", False):
                    stock_config["enabled"] = False
                    self.redis_config_mgr.save_stock_config(symbol, stock_config)
                    logger.info(f"成功禁用股票 {symbol} 的EMA策略。")
                else:
                    logger.info(f"股票 {symbol} 的EMA策略已经是禁用状态。")
                return

            # 如果不是股票，尝试作为ETF处理
            etf_config = self.redis_config_mgr.get_etf_config(symbol)
            if etf_config:
                if etf_config.get("enabled", False):
                    etf_config["enabled"] = False
                    self.redis_config_mgr.save_etf_config(symbol, etf_config)
                    logger.info(f"成功禁用ETF {symbol} 的EMA策略。")
                else:
                    logger.info(f"ETF {symbol} 的EMA策略已经是禁用状态。")
                return

            logger.warning(f"在EMA策略配置中未找到 {symbol}，无法禁用。")

        except Exception as e:
            logger.error(f"禁用 {symbol} 的EMA策略时出错: {e}")

    def buy_stock_all_accounts(self, symbol, pos_percent, msg="一键买入"):
        """买入所有账户的指定股票
        
        Args:
            symbol: 股票代码
            pos_percent: 买入资金比例(0-1)
            msg: 交易备注
            
        Returns:
            dict: 交易结果
        """
        try:
            # 参数验证
            if not symbol:
                return {"status": "error", "message": "股票代码不能为空"}
            
            if pos_percent <= 0 or pos_percent > 1:
                return {"status": "error", "message": "买入比例必须在0-1之间"}
            
            logger.info(f"开始执行一键买入: {symbol}, 比例: {pos_percent}")
            result = self.trade_mgr.buy_stock_for_all_account(symbol, pos_percent, msg)
            logger.info(f"买入股票 {symbol} ({pos_percent*100}%) 结果: {result}")
            return result
        except Exception as e:
            logger.error(f"Error buying stock {symbol}: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    def search_stock(self, keyword):
        """搜索股票，如果找不到则搜索ETF（特别是1开头和5开头的代码）
        
        Args:
            keyword: 搜索关键词（股票代码或名称）
            
        Returns:
            list: 股票搜索结果
        """
        try:
            if not keyword:
                return []
            
            keyword = keyword.strip().upper()
            results = []
            
            # 首先搜索股票数据
            df = data_mgr.get_all_stock_info_df()
            if df is not None and not df.empty:
                # 搜索逻辑：按代码或名称模糊匹配
                mask = (df['symbol'].astype(str).str.contains(keyword, na=False)) | \
                       (df['name'].astype(str).str.contains(keyword, na=False))
                
                result_df = df[mask].head(15)  # 限制返回15条股票结果
                
                for _, row in result_df.iterrows():
                    try:
                        results.append({
                            "symbol": str(row['symbol']),
                            "name": str(row['name']),
                            "close": float(row.get('close', 0) or 0),
                            "changePercent": float(row.get('changePercent', 0) or 0),
                            "type": "stock"
                        })
                    except (ValueError, TypeError) as e:
                        logger.warning(f"处理股票数据时出错: {row}, 错误: {e}")
                        continue
            
            # 如果股票结果少于10条，或者关键词以1或5开头，则搜索ETF数据
            if len(results) < 10 or keyword.startswith('1') or keyword.startswith('5'):
                try:
                    etf_df = data_mgr.get_all_etf_df()
                    if etf_df is not None and not etf_df.empty:
                        logger.info(f"搜索ETF数据，关键词: {keyword}")
                        
                        # ETF数据可能使用不同的列名，需要适配
                        # 检查列名，适配不同的数据格式
                        symbol_col = 'symbol' if 'symbol' in etf_df.columns else ('代码' if '代码' in etf_df.columns else None)
                        name_col = 'name' if 'name' in etf_df.columns else ('名称' if '名称' in etf_df.columns else None)
                        close_col = 'close' if 'close' in etf_df.columns else ('最新价' if '最新价' in etf_df.columns else None)
                        change_col = 'changePercent' if 'changePercent' in etf_df.columns else ('涨跌幅' if '涨跌幅' in etf_df.columns else None)
                        
                        if symbol_col and name_col:
                            # 搜索ETF：按代码或名称模糊匹配
                            etf_mask = (etf_df[symbol_col].astype(str).str.contains(keyword, na=False)) | \
                                      (etf_df[name_col].astype(str).str.contains(keyword, na=False))
                            
                            etf_result_df = etf_df[etf_mask].head(5)  # 限制返回5条ETF结果
                            
                            for _, row in etf_result_df.iterrows():
                                try:
                                    close_price = 0
                                    change_percent = 0
                                    
                                    if close_col and close_col in row:
                                        close_price = float(row.get(close_col, 0) or 0)
                                    if change_col and change_col in row:
                                        change_percent = float(row.get(change_col, 0) or 0)
                                    
                                    results.append({
                                        "symbol": str(row[symbol_col]),
                                        "name": str(row[name_col]) + " (ETF)",
                                        "close": close_price,
                                        "changePercent": change_percent,
                                        "type": "etf"
                                    })
                                    logger.info(f"找到ETF: {row[symbol_col]} -> {row[name_col]}")
                                except (ValueError, TypeError) as e:
                                    logger.warning(f"处理ETF数据时出错: {row}, 错误: {e}")
                                    continue
                        else:
                            logger.warning(f"ETF数据列名不匹配，可用列: {etf_df.columns.tolist()}")
                            
                except Exception as e:
                    logger.warning(f"搜索ETF数据时出错: {e}")
            
            # 限制总结果数量
            results = results[:20]
            
            if not results:
                logger.warning(f"未找到匹配的股票或ETF: {keyword}")
            else:
                logger.info(f"搜索关键词 '{keyword}' 找到 {len(results)} 条结果")
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching stock/ETF with keyword {keyword}: {str(e)}")
            return []

    def get_trading_history(self, limit=50):
        """获取交易历史记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            list: 交易历史记录
        """
        try:
            # 这里可以从日志或数据库中获取交易历史
            # 暂时返回空列表，后续可以扩展
            return []
        except Exception as e:
            logger.error(f"Error getting trading history: {str(e)}")
            return []

    def toggle_account_status(self, account_type, status):
        """切换账户的启用/禁用状态"""
        try:
            if not account_type:
                return {"status": "error", "message": "账户类型不能为空"}
            if status not in ['open', 'close']:
                return {"status": "error", "message": "无效的状态值"}

            redis_mgr = StockRedisMgr()
            redis_key = redis_mgr.build_key(RedisKeyPrefix.ACCOUNT_CONFIG.value, "status")
            
            success = redis_mgr.hset_value(redis_key, account_type, status)

            if success:
                logger.info(f"账户 {account_type} 状态已更新为: {status}")
                return {"status": "success", "message": f"账户 {account_type} 状态已更新为 {status}"}
            else:
                logger.error(f"更新账户 {account_type} 状态到Redis失败")
                return {"status": "error", "message": "更新账户状态失败"}
        except Exception as e:
            logger.error(f"切换账户 {account_type} 状态时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    def get_total_position(self):
        """获取总仓位设置"""
        try:
            position = config.get_position()
            return {"status": "success", "position": position}
        except Exception as e:
            logger.error(f"获取总仓位设置失败: {str(e)}")
            return {"status": "error", "message": "获取总仓位设置失败"}

    def set_total_position(self, position):
        """设置总仓位"""
        try:
            if not isinstance(position, (float, int)) or not (0 <= position <= 1):
                return {"status": "error", "message": "仓位值必须是0到1之间的数字"}

            redis_mgr = StockRedisMgr()
            redis_key = redis_mgr.build_key(RedisKeyPrefix.ACCOUNT_CONFIG.value, "position")
            success = redis_mgr.set_value(redis_key, str(position))

            if success:
                logger.info(f"总仓位已更新为: {position}")
                return {"status": "success", "message": f"总仓位已更新为 {position}"}
            else:
                logger.error("更新总仓位到Redis失败")
                return {"status": "error", "message": "更新总仓位失败"}
        except Exception as e:
            logger.error(f"设置总仓位时出错: {str(e)}")
            return {"status": "error", "message": "设置总仓位失败"}

    def sell_stock_single_account(self, account_type, symbol, sell_quantity, msg="单账户卖出"):
        """卖出单个账户的指定股票指定数量"""
        try:
            result = account_mgr.sell_stock(account_type, symbol, sell_pct=0, msg=msg, sell_count=sell_quantity)
            if result:
                return {"status": "success", "message": "卖出操作已提交"}
            else:
                return {"status": "error", "message": "卖出操作失败"}
        except Exception as e:
            logger.error(f"卖出单个账户股票失败: {str(e)}")
            return {"status": "error", "message": f"卖出操作异常: {str(e)}"}

    def get_account_delivery_info(self, account_type):
        """获取账户交割单信息"""
        try:
            if account_type not in account_mgr.account_types:
                return {"status": "error", "message": f"未知的账户类型: {account_type}"}
            
            account_id = account_mgr.account_types[account_type]
            delivery_df = account_mgr.get_account_delivery_info(account_id)
            
            if delivery_df.empty:
                return {"status": "success", "data": []}
            
            # 转换DataFrame为前端需要的格式
            delivery_list = []
            for _, row in delivery_df.iterrows():
                delivery_item = {
                    "platformOrderId": str(row.get("platformOrderId", "")),
                    "account": str(row.get("account", "")),
                    "symbol": str(row.get("symbol", "")),
                    "name": str(row.get("name", "")),
                    "tradeType": str(row.get("tradeType", "")),
                    "tradePrice": float(row.get("tradePrice", 0)),
                    "tradeAmount": float(row.get("tradeAmount", 0)),
                    "tradeGold": float(row.get("tradeGold", 0)),
                    "realTradeGold": float(row.get("realTradeGold", 0)),
                    "commission": float(row.get("commission", 0)),
                    "transactionFee": float(row.get("transactionFee", 0)),
                    "stampDuty": float(row.get("stampDuty", 0)),
                    "transferFee": float(row.get("transferFee", 0)),
                    "stockBalance": float(row.get("stockBalance", 0)),
                    "accountBalance": float(row.get("accountBalance", 0)),
                    "createTime": str(row.get("createTime", "")),
                    "changePercent": float(row.get("changePercent", 0)) if pd.notna(row.get("changePercent")) else 0,
                    "latestPrice": float(row.get("latestPrice", 0)) if pd.notna(row.get("latestPrice")) else 0
                }
                delivery_list.append(delivery_item)
            
            return {"status": "success", "data": delivery_list}
            
        except Exception as e:
            logger.error(f"获取账户交割单信息失败: {str(e)}")
            return {"status": "error", "message": f"获取交割单信息失败: {str(e)}"}

    def get_account_profit_info(self, account_type, month):
        """获取账户收益日历信息"""
        try:
            if not account_type:
                return {"status": "error", "message": "账户类型不能为空"}
            
            # 处理月份参数
            if month:
                # 将YYYY-MM格式转换为YYYYMM格式
                month_str = month.replace('-', '')
                # 验证月份格式
                if len(month_str) != 6 or not month_str.isdigit():
                    return {"status": "error", "message": "月份格式错误，应为YYYY-MM"}
            else:
                from datetime import datetime
                month_str = datetime.now().strftime('%Y%m')

            logger.info(f"获取账户收益信息: {account_type}, 月份: {month_str}")
            
            # 调用账户管理器获取收益信息
            profit_info_list = account_mgr.get_specific_account_profit_info(account_type, month_str)
            
            if profit_info_list:
                profit_info = profit_info_list[0]
                logger.info(f"获取到收益信息: {profit_info}")
                
                # 确保数据类型正确
                profit_info['totalProfit'] = float(profit_info.get('totalProfit', 0))
                if 'profitInfoGroup' in profit_info and profit_info['profitInfoGroup']:
                    for daily_profit in profit_info['profitInfoGroup']:
                        daily_profit['profit'] = float(daily_profit.get('profit', 0))
                        daily_profit['profitRate'] = float(daily_profit.get('profitRate', 0))
                        # 确保交易日期格式正确
                        if 'tradeDate' in daily_profit:
                            daily_profit['tradeDate'] = str(daily_profit['tradeDate'])
                
                return {"status": "success", "data": profit_info}
            else:
                logger.info(f"未找到账户 {account_type} 在 {month_str} 的收益信息，返回空数据")
                # 返回空数据结构
                return {"status": "success", "data": {
                    'account': account_mgr.account_types.get(account_type, account_type),
                    'monthDate': month_str,
                    'totalProfit': 0.0,
                    'profitInfoGroup': []
                }}
                
        except Exception as e:
            logger.error(f"获取账户收益信息失败 for {account_type}, month {month}: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": f"获取账户收益信息失败: {str(e)}"}

    def buy_stock_single_account(self, account_type, symbol, buy_amount, msg="单账户买入"):
        """买入单个账户的指定股票的指定金额
        
        Args:
            account_type: 账户类型
            symbol: 股票代码
            buy_amount: 买入金额
            msg: 交易备注
            
        Returns:
            dict: 交易结果
        """
        try:
            # 参数验证
            if not account_type:
                return {"status": "error", "message": "账户类型不能为空"}
            
            if not symbol:
                return {"status": "error", "message": "股票代码不能为空"}
            
            if buy_amount <= 0:
                return {"status": "error", "message": "买入金额必须大于0"}
            
            logger.info(f"开始执行单账户买入: {account_type}, {symbol}, 金额: {buy_amount}")
            result = self.trade_mgr.buy_stock_single_account(account_type, symbol, buy_amount, msg)
            logger.info(f"买入股票 {symbol} ({buy_amount}元) 结果: {result}")
            return result
        except Exception as e:
            logger.error(f"Error buying stock {symbol} for account {account_type}: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    def get_account_statistics(self, account_type):
        """获取账户统计信息（用于扩展功能）"""
        try:
            if not account_type:
                return {"status": "error", "message": "账户类型不能为空"}
            
            account_info = account_mgr.get_account_datainfo(account_type)
            if not account_info:
                return {"status": "error", "message": f"无法获取账户 {account_type} 信息"}
            
            if isinstance(account_info, list) and len(account_info) > 0:
                account_info = account_info[0]
            
            if not isinstance(account_info, dict):
                return {"status": "error", "message": f"账户 {account_type} 数据格式错误"}
            
            # 计算统计信息
            total_profit = float(account_info.get("totalProfit", 0))
            total_today_profit = float(account_info.get("totalTodayProfit", 0))
            total_cash = float(account_info.get("totalCash", 0))
            market_value = float(account_info.get("marketValue", 0))
            
            # 计算收益率
            profit_rate = (total_profit / (total_cash - total_profit) * 100) if (total_cash - total_profit) > 0 else 0
            
            # 统计持仓信息
            position_count = 0
            profit_positions = 0
            loss_positions = 0
            
            if "positionInfoGroup" in account_info and account_info["positionInfoGroup"]:
                position_count = len(account_info["positionInfoGroup"])
                for pos in account_info["positionInfoGroup"]:
                    if not isinstance(pos, dict):
                        continue
                    
                    trade_price = float(pos.get("tradePrice", 0))
                    trade_amount = int(pos.get("tradeAmount", 0))
                    position_gold = float(pos.get("positionGold", 0))
                    cost = trade_price * trade_amount
                    profit = position_gold - cost
                    
                    if profit > 0:
                        profit_positions += 1
                    elif profit < 0:
                        loss_positions += 1
            
            return {
                "status": "success",
                "data": {
                    "account_type": account_type,
                    "total_profit": total_profit,
                    "total_today_profit": total_today_profit,
                    "profit_rate": profit_rate,
                    "position_count": position_count,
                    "profit_positions": profit_positions,
                    "loss_positions": loss_positions,
                    "flat_positions": position_count - profit_positions - loss_positions
                }
            }
            
        except Exception as e:
            logger.error(f"获取账户统计信息失败: {str(e)}")
            return {"status": "error", "message": f"获取账户统计信息失败: {str(e)}"}

    def validate_trading_time(self):
        """验证交易时间（用于扩展功能）"""
        try:
            from datetime import datetime, time
            
            now = datetime.now()
            current_time = now.time()
            
            # 交易时间段
            morning_start = time(9, 30)
            morning_end = time(11, 30)
            afternoon_start = time(13, 0)
            afternoon_end = time(15, 0)
            
            # 检查是否在交易时间内
            is_trading_time = (
                (morning_start <= current_time <= morning_end) or
                (afternoon_start <= current_time <= afternoon_end)
            )
            
            # 检查是否是工作日（周一到周五）
            is_weekday = now.weekday() < 5
            
            return {
                "status": "success",
                "data": {
                    "is_trading_time": is_trading_time and is_weekday,
                    "current_time": now.strftime("%H:%M:%S"),
                    "is_weekday": is_weekday,
                    "trading_status": "交易时间" if (is_trading_time and is_weekday) else "非交易时间"
                }
            }
            
        except Exception as e:
            logger.error(f"验证交易时间失败: {str(e)}")
            return {"status": "error", "message": f"验证交易时间失败: {str(e)}"} 
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一键交易管理</title>
    <!-- prevent browser cache -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <!-- Import Vue 3 -->
    <script src="js/vue.global.min.js"></script>
    <!-- Import Element Plus -->
    <script src="js/element-plus.js"></script>
    <!-- Import Element Plus CSS -->
    <link rel="stylesheet" href="css/index.css" />
    <script src="js/element-plus-icons-vue.js"></script>
    <script src="js/babel.min.js"></script>

    <style>
        [v-cloak] {
            display: none;
        }
        .page-container {
            margin: 20px;
            min-height: calc(100vh - 40px);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            border-bottom: 1px solid #e4e7ed;
            margin-bottom: 20px;
        }

        .header-title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-right: 20px;
        }



        .action-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .action-card {
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 20px;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .card-title .icon {
            margin-right: 8px;
            font-size: 20px;
        }

        .form-item {
            margin-bottom: 15px;
        }

        .form-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #606266;
        }

        .danger-action {
            border-color: #f56c6c;
            background: #fef0f0;
        }

        .danger-action .card-title {
            color: #f56c6c;
        }

        .warning-action {
            border-color: #e6a23c;
            background: #fdf6ec;
        }

        .warning-action .card-title {
            color: #e6a23c;
        }

        .primary-action {
            border-color: #409eff;
            background: #ecf5ff;
        }

        .primary-action .card-title {
            color: #409eff;
        }

        .account-overview {
            margin-bottom: 30px;
        }

        .account-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .account-card {
            border: 1px solid #dcdfe6;
            border-radius: 6px;
            padding: 15px;
            background: #fff;
        }

        .account-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .account-name {
            font-weight: 600;
            color: #303133;
        }

        .account-stats {
            font-size: 12px;
            color: #909399;
        }

        .account-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 14px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
        }

        .info-label {
            color: #909399;
        }

        .info-value {
            color: #303133;
            font-weight: 500;
        }

        .result-container {
            margin-top: 30px;
        }

        .result-card {
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 20px;
            background: #fff;
            margin-bottom: 15px;
        }

        .result-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .result-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
        }

        .result-time {
            font-size: 12px;
            color: #909399;
        }

        .result-content {
            max-height: 300px;
            overflow-y: auto;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
        }

        .stock-search-result {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }

        .stock-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
        }

        .stock-item:hover {
            background: #f5f7fa;
        }

        .stock-item:last-child {
            border-bottom: none;
        }

        .stock-symbol {
            font-weight: 600;
            color: #303133;
        }

        .stock-name {
            color: #606266;
            margin-left: 10px;
        }

        .stock-price {
            float: right;
            color: #909399;
        }

        .positive {
            color: #f56c6c;
        }

        .negative {
            color: #67c23a;
        }

        .profit-positive {
            color: #f56565 !important;
            font-weight: bold;
        }

        .profit-negative {
            color: #48bb78 !important;
            font-weight: bold;
        }

        .profit-zero {
            color: #a0aec0 !important;
        }

        .position-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .position-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .position-item:last-child {
            border-bottom: none;
        }

        .position-stock {
            flex: 1;
        }

        .position-value {
            text-align: right;
            font-size: 12px;
            color: #909399;
        }

        .position-action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
        }

        .position-action-buttons .el-button {
            padding: 5px 10px;
            font-size: 12px;
        }

        /* 密码锁样式 */
        .password-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }

        .password-dialog {
            background: #fff;
            padding: 40px;
            border-radius: 8px;
            text-align: center;
            min-width: 300px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .password-dialog h3 {
            margin-bottom: 20px;
            color: #303133;
            font-size: 18px;
        }

        .password-input {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            margin-bottom: 20px;
            text-align: center;
        }

        .password-input:focus {
            outline: none;
            border-color: #409eff;
        }

        .password-btn {
            width: 100%;
            padding: 12px;
            background: #409eff;
            color: #fff;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }

        .password-btn:hover {
            background: #337ecc;
        }

        .password-btn:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }

        .password-error {
            color: #f56c6c;
            margin-top: 10px;
            font-size: 14px;
            min-height: 20px;
        }

        .password-input.error {
            border-color: #f56c6c;
            animation: shake 0.5s;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .password-success {
            color: #67c23a;
            margin-top: 10px;
            font-size: 14px;
        }

        .page-locked {
            filter: blur(3px);
            pointer-events: none;
        }

        /* 账户详情弹窗样式 */
        .account-detail-dialog {
            border-radius: 12px;
            overflow: hidden;
        }

        .account-detail-dialog .el-dialog__header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 12px 12px 0 0;
        }

        .dialog-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dialog-title {
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .dialog-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .account-detail-content {
            padding: 0;
        }

        /* 账户概览卡片 */
        .account-overview-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .overview-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .overview-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
        }

        .card-icon {
            font-size: 32px;
            margin-right: 15px;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            background: #f0f2f5;
        }

        .card-icon.total-assets {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
        }

        .card-icon.available-cash {
            background: linear-gradient(135deg, #67c23a, #85ce61);
        }

        .card-icon.market-value {
            background: linear-gradient(135deg, #409eff, #66b1ff);
        }

        .card-icon.profit {
            background: linear-gradient(135deg, #f56c6c, #f78989);
        }

        .card-icon.profit.profit-positive {
            background: linear-gradient(135deg, #f56c6c, #f78989);
        }

        .card-icon.profit.profit-negative {
            background: linear-gradient(135deg, #67c23a, #85ce61);
        }

        .card-icon.today-profit {
            background: linear-gradient(135deg, #e6a23c, #ebb563);
        }

        .card-icon.today-profit.profit-positive {
            background: linear-gradient(135deg, #f56c6c, #f78989);
        }

        .card-icon.today-profit.profit-negative {
            background: linear-gradient(135deg, #67c23a, #85ce61);
        }

        .card-icon.position-count {
            background: linear-gradient(135deg, #909399, #b1b3b8);
        }

        .card-content {
            flex: 1;
        }

        .card-value {
            font-size: 24px;
            font-weight: 700;
            color: #303133;
            margin-bottom: 4px;
        }

        .card-label {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
        }

        /* 持仓明细区域 */
        .positions-section {
            padding: 30px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-header h3 {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            margin: 0;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        /* 表格样式增强 */
        .el-table {
            border-radius: 8px;
            overflow: hidden;
        }

        .el-table .el-table__header-wrapper {
            background: #f5f7fa;
        }

        .el-table .el-table__row:hover {
            background: #f0f9ff;
        }

        .profit-cell {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        /* 持仓占比进度条颜色 */
        .getProgressColor {
            color: #409eff;
        }

        /* 表格行样式 */
        .el-table .profit-row {
            background: #f0f9ff;
        }

        .el-table .loss-row {
            background: #fef0f0;
        }

        /* 按钮样式增强 */
        .position-action-buttons .el-button {
            border-radius: 6px;
            font-weight: 500;
        }

        .position-action-buttons .el-button--primary {
            background: linear-gradient(135deg, #409eff, #66b1ff);
            border: none;
        }

        .position-action-buttons .el-button--danger {
            background: linear-gradient(135deg, #f56c6c, #f78989);
            border: none;
        }

        /* 标签样式 */
        .el-tag {
            border-radius: 6px;
            font-weight: 500;
        }

        /* 交割单弹窗样式 */
        .delivery-info-dialog {
            border-radius: 12px;
            overflow: hidden;
        }

        .delivery-info-dialog .el-dialog__header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 12px 12px 0 0;
        }

        .delivery-info-content {
            padding: 20px;
        }

        .delivery-filters {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-group label {
            font-size: 14px;
            font-weight: 500;
            color: #606266;
            white-space: nowrap;
        }

        .fee-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4px;
            font-size: 12px;
        }

        .fee-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .fee-label {
            color: #909399;
            font-weight: 500;
        }

        .fee-value {
            color: #303133;
            font-weight: 600;
        }

        /* 交割单表格自适应优化 */
        .delivery-info-dialog .el-table {
            table-layout: auto;
        }

        .delivery-info-dialog .el-table th,
        .delivery-info-dialog .el-table td {
            padding: 12px 8px;
        }

        .delivery-info-dialog .el-table .cell {
            word-break: break-word;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .account-overview-cards {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
                padding: 20px;
            }

            .overview-card {
                padding: 15px;
            }

            .card-icon {
                font-size: 24px;
                width: 40px;
                height: 40px;
                margin-right: 10px;
            }

            .card-value {
                font-size: 18px;
            }

            .positions-section {
                padding: 20px;
            }

            .delivery-filters {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .filter-group {
                flex-direction: column;
                align-items: stretch;
                gap: 5px;
            }

            .filter-group label {
                text-align: left;
            }

            .delivery-info-content {
                padding: 15px;
            }

            .fee-details {
                grid-template-columns: 1fr;
                gap: 2px;
            }

            /* 移动端表格优化 */
            .delivery-info-dialog .el-table th,
            .delivery-info-dialog .el-table td {
                padding: 8px 4px;
                font-size: 12px;
            }

            .delivery-info-dialog .el-table .el-tag {
                font-size: 10px;
                padding: 2px 4px;
            }
        }

        /* 中等屏幕优化 */
        @media (min-width: 769px) and (max-width: 1200px) {
            .delivery-filters {
                flex-wrap: wrap;
                gap: 15px;
            }

            .filter-group {
                flex-direction: column;
                align-items: stretch;
                min-width: 150px;
            }

            .filter-group label {
                margin-bottom: 5px;
            }

            /* 中等屏幕表格优化 */
            .delivery-info-dialog .el-table th,
            .delivery-info-dialog .el-table td {
                padding: 10px 6px;
                font-size: 13px;
            }
        }

        /* 大屏幕优化 */
        @media (min-width: 1201px) {
            .delivery-info-dialog .el-dialog {
                max-width: 1400px;
            }
        }

        /* 收益日历样式 */
        .profit-calendar-dialog {
            border-radius: 12px;
            overflow: hidden;
        }

        .profit-calendar-dialog .el-dialog__header {
            padding: 20px 25px;
            background: #f8f9fa;
            color: #333;
            border-bottom: 1px solid #e9ecef;
        }

        .profit-calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .profit-calendar-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
        }

        .profit-calendar-title i {
            margin-right: 10px;
            font-size: 24px;
        }
        
        .profit-calendar-content {
            padding: 20px;
            background: #f8f9fa;
        }

        .calendar-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .calendar-controls h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .calendar-controls-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .calendar-weekdays {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            text-align: center;
            padding: 10px 0;
            font-weight: 600;
            color: #666;
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .calendar-day {
            position: relative;
            height: 120px;
            padding: 10px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: box-shadow 0.2s ease;
        }
        
        .calendar-day:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .calendar-day.is-other-month {
            background: #f8f9fa;
            color: #ccc;
        }
        
        .calendar-day.is-today {
            border-color: #409eff;
            box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
        }
        
        .day-number {
            font-size: 40px;
            font-weight: 800;
            color: #222;
            text-align: center;
            line-height: 1.1;
            margin-bottom: 8px;
        }
        
        .day-profit {
            font-size: 36px;
            font-weight: 600;
            text-align: center;
            margin: 0;
        }

        .day-profit.profit-positive {
            color: #f56c6c;
        }

        .day-profit.profit-negative {
            color: #67c23a;
        }

        /* 统计卡片样式 */
        .calendar-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: box-shadow 0.2s ease;
        }

        .stat-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f2f5;
            border-radius: 8px;
        }

        .stat-content {
            flex: 1;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .profit-calendar-content {
                padding: 15px;
            }

            .calendar-controls {
                flex-direction: column;
                gap: 15px;
                padding: 15px;
            }

            .calendar-controls-right {
                flex-direction: column;
                gap: 10px;
            }

            .calendar-day {
                height: 100px;
                padding: 8px;
            }

            .day-number {
                font-size: 14px;
            }

            .day-profit {
                font-size: 24px;
            }

            .calendar-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .stat-card {
                padding: 15px;
                gap: 10px;
            }

            .stat-icon {
                font-size: 20px;
                width: 32px;
                height: 32px;
            }

            .stat-value {
                font-size: 16px;
            }
        }

        /* 加载状态样式 */
        .calendar-loading {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .calendar-loading .el-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: #409eff;
        }

        .calendar-loading div {
            font-size: 16px;
        }

        /* 空数据状态样式 */
        .calendar-empty {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .calendar-empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            color: #ddd;
        }

        .calendar-empty-text {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .calendar-empty-tip {
            font-size: 14px;
            color: #999;
        }
    </style>
</head>

<body>
    <div id="app" v-cloak>
        <!-- 密码锁遮罩 -->
        <div v-if="showPasswordLock" class="password-overlay">
            <div class="password-dialog">
                <h3>🔒 请输入访问密码</h3>
                <input 
                    v-model="passwordInput" 
                    type="password" 
                    class="password-input" 
                    :class="{ 'error': passwordInputError }"
                    placeholder="请输入密码"
                    @keyup.enter="checkPassword"
                    :disabled="passwordChecking"
                    ref="passwordInputRef">
                <button @click="checkPassword" class="password-btn" :disabled="passwordChecking || !passwordInput">
                    {{ passwordChecking ? '验证中...' : '确认' }}
                </button>
                <div v-if="passwordError" class="password-error">{{ passwordError }}</div>
                <div v-if="passwordSuccess" class="password-success">{{ passwordSuccess }}</div>
            </div>
        </div>

        <div class="page-container" :class="{ 'page-locked': showPasswordLock }">


            <div class="header">
                <div style="display: flex; align-items: center; gap: 20px;">
                    <div class="header-title">一键交易管理</div>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <el-button @click="refreshAccountInfo" size="small" type="primary">刷新账户信息</el-button>
                        <el-button @click="lockPage" size="small" type="warning">🔒 锁定</el-button>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <el-button @click="goToMonitorPage" size="small" type="info">监控打板</el-button>
                    <el-button @click="goToStrategyTrade" size="small" type="success">策略交易</el-button>
                </div>
            </div>

            <!-- 账户概览 -->
            <div class="account-overview">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                    <h3 style="margin: 0;">账户概览</h3>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <label style="font-size: 14px; color: #606266;">总仓位:</label>
                        <el-input-number 
                            v-model="totalPosition" 
                            :min="0" 
                            :max="100" 
                            :step="1" 
                            size="small" 
                            controls-position="right"
                            style="width: 100px;">
                        </el-input-number>
                        <span style="font-size: 14px; color: #606266; margin-left: -5px;">%</span>
                        <el-button @click="saveTotalPosition" size="small" type="primary" :loading="loading.position">保存</el-button>
                    </div>
                </div>
                <div class="account-grid">
                    <div v-for="(account, accountType) in accountsInfo" :key="accountType" class="account-card">
                        <div class="account-card-header">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <span class="account-name">{{ accountType }}</span>
                                <el-button
                                    v-if="typeof account.status !== 'undefined'"
                                    @click="toggleAccountStatus(accountType, account.status)"
                                    :type="account.status === 'open' ? 'danger' : 'success'"
                                    size="small" plain round
                                    :loading="statusLoading[accountType]">
                                    {{ account.status === 'open' ? '关闭' : '启用' }}
                                </el-button>
                            </div>
                            <span class="account-stats">{{ account.positionCount }} 持仓</span>
                        </div>
                        <div class="account-info">
                            <div class="info-item">
                                <span class="info-label">总资产:</span>
                                <span class="info-value">{{ formatMoney(account.totalCash) }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">可用资金:</span>
                                <span class="info-value">{{ formatMoney(account.availableCash) }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">市值:</span>
                                <span class="info-value">{{ formatMoney(account.marketValue) }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">盈亏:</span>
                                <span class="info-value" :class="getProfitClass(account.totalProfit)">{{ formatProfit(account.totalProfit) }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">当日:</span>
                                <span class="info-value" :class="getProfitClass(account.totalTodayProfit)">
                                    {{ formatProfit(account.totalTodayProfit) }}
                                </span>
                            </div>
                            <div class="info-item">
                                <span></span>
                                <div>
                                    <el-button @click="viewAccountDetail(accountType)" size="small" type="text">详情</el-button>
                                    <el-button @click="clearSingleAccount(accountType)" size="small" type="danger">清仓</el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作卡片 -->
            <div class="action-cards">
                <!-- 一键清仓账户 -->
                <div class="action-card danger-action">
                    <div class="card-title">
                        <i class="icon">⚠️</i>
                        一键清仓账户
                    </div>
                    <div class="form-item">
                        <label>选择账户:</label>
                        <el-select v-model="selectedClearAccount" placeholder="请选择要清仓的账户" style="width: 100%;">
                            <el-option 
                                key="ALL_ACCOUNTS" 
                                label="🔥 清仓所有账户" 
                                value="ALL_ACCOUNTS"
                                style="color: #F56C6C; font-weight: bold;">
                            </el-option>
                            <el-option 
                                v-for="(account, accountType) in accountsInfo" 
                                :key="accountType" 
                                :label="`${accountType} (${account.positionCount}持仓)`" 
                                :value="accountType">
                            </el-option>
                        </el-select>
                    </div>
                    <div class="form-item">
                        <label>排除股票代码（可选）<span style="color: #909399; font-size: 12px; margin-left: 5px;">排除的股票在清仓时不会被卖出</span>:</label>
                        <el-input 
                            v-model="excludeStockSymbols" 
                            type="textarea"
                            :rows="1"
                            placeholder="请输入要排除的股票代码，多个代码用逗号、空格分隔，例如: 000001,000002"
                            style="margin-bottom: 10px;">
                        </el-input>
                    </div>
                    <div class="form-item">
                        <label>交易备注:</label>
                        <el-input v-model="clearMsg" placeholder="请输入交易备注"></el-input>
                    </div>
                    <el-button @click="executeClearOperation" 
                               type="danger" 
                               :loading="loading.clear" 
                               :disabled="!selectedClearAccount"
                               style="width: 100%;">
                        {{ getClearButtonText() }}
                    </el-button>
                </div>

                <!-- 一键卖出指定股票 -->
                <div class="action-card warning-action">
                    <div class="card-title">
                        <i class="icon">📉</i>
                        一键卖出指定股票
                    </div>
                    <div class="form-item">
                        <label>股票代码/名称:</label>
                        <el-input v-model="sellStockSymbol" 
                                  @input="searchStock" 
                                  placeholder="输入股票代码或名称搜索">
                        </el-input>
                        <div v-if="stockSearchResults.length > 0" class="stock-search-result">
                            <div v-for="stock in stockSearchResults" 
                                 :key="stock.symbol" 
                                 class="stock-item"
                                 @click="selectStock(stock, 'sell')">
                                <span class="stock-symbol">{{ stock.symbol }}</span>
                                <span class="stock-name">{{ stock.name }}</span>
                                <span class="stock-price" :class="stock.changePercent >= 0 ? 'positive' : 'negative'">
                                    {{ stock.close }} ({{ stock.changePercent.toFixed(2) }}%)
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <label>卖出比例:</label>
                        <el-slider v-model="sellPercent" 
                                   :min="1" 
                                   :max="100" 
                                   :step="1"
                                   show-input
                                   :format-tooltip="val => val + '%'">
                        </el-slider>
                    </div>
                    <div class="form-item">
                        <label>交易备注:</label>
                        <el-input v-model="sellMsg" placeholder="请输入交易备注"></el-input>
                    </div>
                    <el-button @click="() => { console.log('🖱️ 卖出按钮被点击了！'); sellStock(); }" 
                               type="warning" 
                               :loading="loading.sell" 
                               :disabled="!sellStockSymbol"
                               style="width: 100%;">
                        卖出 {{ sellStockSymbol }} ({{ sellPercent }}%)
                    </el-button>
                </div>

                <!-- 一键买入指定股票 -->
                <div class="action-card primary-action">
                    <div class="card-title">
                        <i class="icon">📈</i>
                        一键买入指定股票
                    </div>
                    <div class="form-item">
                        <label>股票代码/名称:</label>
                        <el-input v-model="buyStockSymbol" 
                                  @input="searchStock" 
                                  placeholder="输入股票代码或名称搜索">
                        </el-input>
                        <div v-if="stockSearchResults.length > 0 && buyStockSymbol" class="stock-search-result">
                            <div v-for="stock in stockSearchResults" 
                                 :key="stock.symbol" 
                                 class="stock-item"
                                 @click="selectStock(stock, 'buy')">
                                <span class="stock-symbol">{{ stock.symbol }}</span>
                                <span class="stock-name">{{ stock.name }}</span>
                                <span class="stock-price" :class="stock.changePercent >= 0 ? 'positive' : 'negative'">
                                    {{ stock.close }} ({{ stock.changePercent.toFixed(2) }}%)
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <label>买入资金比例:</label>
                        <el-slider v-model="buyPercent" 
                                   :min="1" 
                                   :max="100" 
                                   :step="1"
                                   show-input
                                   :format-tooltip="val => val + '%'">
                        </el-slider>
                    </div>
                    <div class="form-item">
                        <label>交易备注:</label>
                        <el-input v-model="buyMsg" placeholder="请输入交易备注"></el-input>
                    </div>
                    <el-button @click="buyStock" 
                               type="primary" 
                               :loading="loading.buy" 
                               :disabled="!buyStockSymbol"
                               style="width: 100%;">
                        买入 {{ buyStockSymbol }} ({{ buyPercent }}%)
                    </el-button>
                </div>
            </div>

            <!-- 交易结果显示 -->
            <div v-if="tradeResults.length > 0" class="result-container">
                <h3>交易结果</h3>
                <div v-for="(result, index) in tradeResults" :key="index" class="result-card">
                    <div class="result-header">
                        <span class="result-title">{{ result.title }}</span>
                        <span class="result-time">{{ result.time }}</span>
                    </div>
                    <div class="result-content">
                        <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载遮罩 -->
        <div v-if="showLoading" class="loading-overlay">
            <div class="loading-content">
                <el-icon class="is-loading" style="font-size: 30px; margin-bottom: 10px;"><Loading /></el-icon>
                <p v-text="loadingText"></p>
            </div>
        </div>

        <!-- 账户详情对话框 -->
        <el-dialog 
            v-model="accountDetailVisible" 
            width="90%" 
            :before-close="handleCloseAccountDetail"
            class="account-detail-dialog">
            <template #header>
                <div class="dialog-header">
                    <div class="dialog-title">
                        <i class="el-icon-user" style="margin-right: 8px;"></i>
                        <span>{{ selectedAccountDetail?.account_type }} - 账户详情</span>
                    </div>
                    <div class="dialog-subtitle">
                        <span>更新时间：{{ new Date().toLocaleString() }}</span>
                    </div>
                </div>
            </template>
            
            <div v-if="selectedAccountDetail" class="account-detail-content">
                <!-- 账户概览卡片 -->
                <div class="account-overview-cards">
                    <div class="overview-card">
                        <div class="card-icon total-assets">💰</div>
                        <div class="card-content">
                            <div class="card-value">{{ formatMoney(selectedAccountDetail.totalCash) }}</div>
                            <div class="card-label">总资产</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon available-cash">💵</div>
                        <div class="card-content">
                            <div class="card-value">{{ formatMoney(selectedAccountDetail.availableCash) }}</div>
                            <div class="card-label">可用资金</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon market-value">📊</div>
                        <div class="card-content">
                            <div class="card-value">{{ formatMoney(selectedAccountDetail.marketValue) }}</div>
                            <div class="card-label">持仓市值</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon profit" :class="getProfitClass(selectedAccountDetail.totalProfit)">
                            {{ selectedAccountDetail.totalProfit >= 0 ? '📈' : '📉' }}
                        </div>
                        <div class="card-content">
                            <div class="card-value" :class="getProfitClass(selectedAccountDetail.totalProfit)">
                                {{ formatProfit(selectedAccountDetail.totalProfit) }}
                            </div>
                            <div class="card-label">总盈亏</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon today-profit" :class="getProfitClass(selectedAccountDetail.totalTodayProfit)">
                            {{ selectedAccountDetail.totalTodayProfit >= 0 ? '🌟' : '⚡' }}
                        </div>
                        <div class="card-content">
                            <div class="card-value" :class="getProfitClass(selectedAccountDetail.totalTodayProfit)">
                                {{ formatProfit(selectedAccountDetail.totalTodayProfit) }}
                            </div>
                            <div class="card-label">当日盈亏</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon position-count">📋</div>
                        <div class="card-content">
                            <div class="card-value">{{ selectedAccountDetail.positions?.length || 0 }}</div>
                            <div class="card-label">持仓数量</div>
                        </div>
                    </div>
                </div>
                
                <!-- 持仓明细表格 -->
                <div class="positions-section">
                    <div class="section-header">
                        <h3>持仓明细</h3>
                        <div class="section-actions">
                            <el-button size="small" @click="viewProfitCalendar(selectedAccountDetail.account_type)">
                                <i class="el-icon-date"></i> 交易日历
                            </el-button>
                            <el-button size="small" @click="viewDeliveryInfo(selectedAccountDetail.account_type)">
                                <i class="el-icon-document"></i> 交割单
                            </el-button>
                            <el-button size="small" @click="refreshAccountInfo">
                                <i class="el-icon-refresh"></i> 刷新
                            </el-button>
                        </div>
                    </div>
                    
                    <el-table 
                        :data="selectedAccountDetail.positions" 
                        border 
                        stripe
                        style="width: 100%"
                        :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
                        :row-class-name="getRowClassName"
                        empty-text="暂无持仓">
                        <el-table-column prop="symbol" label="代码" width="100" sortable align="center">
                            <template #default="scope">
                                <el-tag size="small" type="info">{{ scope.row.symbol }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="名称" width="120" show-overflow-tooltip></el-table-column>
                                                 <el-table-column prop="strategyInfo" label="买入策略" width="150" show-overflow-tooltip sortable>
                             <template #default="scope">
                                 <el-tag v-if="scope.row.strategyInfo" size="small" type="primary">
                                     {{ scope.row.strategyInfo }}
                                 </el-tag>
                                 <span v-else style="color: #909399;">-</span>
                             </template>
                         </el-table-column>
                        <el-table-column prop="quantity" label="持股数" align="right" sortable>
                            <template #default="scope">
                                <span style="font-weight: 500;">{{ scope.row.quantity.toLocaleString() }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="availableAmount" label="可用数" align="right" sortable>
                            <template #default="scope">
                                <span style="color: #67c23a;">{{ scope.row.availableAmount.toLocaleString() }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="marketValue" label="市值" :formatter="formatMoneyColumn" sortable align="right">
                            <template #default="scope">
                                <span style="font-weight: 600;">{{ formatMoney(scope.row.marketValue) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="持仓占比" sortable :sort-by="(row) => selectedAccountDetail.totalCash > 0 ? row.marketValue / selectedAccountDetail.totalCash : 0" align="right">
                            <template #default="scope">
                                <el-progress 
                                    v-if="selectedAccountDetail.totalCash > 0"
                                    :percentage="((scope.row.marketValue / selectedAccountDetail.totalCash) * 100)"
                                    :format="(percentage) => percentage.toFixed(1) + '%'"
                                    :stroke-width="8"
                                    :color="getProgressColor(scope.row.marketValue / selectedAccountDetail.totalCash)"
                                    style="width: 80px;">
                                </el-progress>
                                <span v-else>-</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="cost" label="成本" :formatter="formatMoneyColumn" align="right"></el-table-column>
                        <el-table-column prop="profit" label="盈亏" :formatter="formatProfitColumn" sortable align="right">
                            <template #default="scope">
                                <div class="profit-cell">
                                    <span :class="getProfitClass(scope.row.profit)" style="font-weight: 600;">
                                        {{ formatProfit(scope.row.profit) }}
                                    </span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="profitPercent" label="盈亏率" sortable align="right">
                            <template #default="scope">
                                <el-tag 
                                    :type="scope.row.profit > 0 ? 'danger' : scope.row.profit < 0 ? 'success' : 'info'"
                                    size="small"
                                    effect="dark">
                                    {{ scope.row.profitPercent.toFixed(2) }}%
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="todayProfit" label="当日盈亏" sortable align="right">
                            <template #default="scope">
                                <span :class="getProfitClass(scope.row.todayProfit)" style="font-weight: 500;">
                                    {{ formatProfit(scope.row.todayProfit) }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="todayProfitPercent" label="当日盈亏比" sortable align="right">
                            <template #default="scope">
                                <span :class="getProfitClass(scope.row.todayProfit)" style="font-weight: 500;">
                                    {{ scope.row.todayProfitPercent ? scope.row.todayProfitPercent.toFixed(2) + '%' : '0.00%' }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="140" fixed="right" align="center">
                            <template #default="scope">
                                <div class="position-action-buttons">
                                    <el-button 
                                        @click="buyStockFromPosition(scope.row)" 
                                        type="primary" 
                                        size="small" 
                                        :loading="positionLoading.buy[scope.row.symbol]"
                                        :icon="Plus">
                                        买入
                                    </el-button>
                                    <el-button 
                                        @click="sellStockFromPosition(scope.row)" 
                                        type="danger" 
                                        size="small" 
                                        :loading="positionLoading.sell[scope.row.symbol]"
                                        :icon="Minus">
                                        卖出
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </el-dialog>

        <!-- 收益日历弹窗 -->
        <el-dialog
            v-model="profitCalendarVisible"
            width="70%"
            :before-close="handleCloseProfitCalendar"
            class="profit-calendar-dialog">
            <template #header>
                <div class="profit-calendar-header">
                    <div class="profit-calendar-title">
                        <i class="el-icon-date" style="margin-right: 8px;"></i>
                        {{ selectedProfitAccountType }} - 交易日历
                    </div>
                </div>
            </template>
            
            <div class="profit-calendar-content">
                <!-- 加载状态 -->
                <div v-if="loading.profitCalendar" class="calendar-loading">
                    <el-icon class="is-loading"><Loading /></el-icon>
                    <div>正在加载收益数据...</div>
                </div>
                
                <!-- 空数据状态 -->
                <div v-else-if="!profitCalendarData.profitInfoGroup || profitCalendarData.profitInfoGroup.length === 0" class="calendar-empty">
                    <div class="calendar-empty-icon">📅</div>
                    <div class="calendar-empty-text">暂无收益数据</div>
                    <div class="calendar-empty-tip">该月份暂无交易记录</div>
                </div>
                
                <!-- 正常显示 -->
                <div v-else>
                    <div class="calendar-controls">
                        <h3>{{ formatCalendarTitle(calendarMonth) }}</h3>
                        <div class="calendar-controls-right">
                            <el-switch
                                v-model="profitDisplayMode"
                                active-text="收益率"
                                inactive-text="收益额"
                                active-value="rate"
                                inactive-value="amount">
                            </el-switch>
                            <el-date-picker
                                v-model="calendarMonth"
                                type="month"
                                placeholder="选择月份"
                                format="YYYY年MM月"
                                value-format="YYYY-MM"
                                @change="handleMonthChange"
                                :clearable="false">
                            </el-date-picker>
                        </div>
                    </div>
                    
                    <div class="calendar-weekdays">
                        <div>周一</div>
                        <div>周二</div>
                        <div>周三</div>
                        <div>周四</div>
                        <div>周五</div>
                    </div>
                    
                    <div class="calendar-grid">
                        <div v-for="day in calendarDays" :key="day.fullDate" 
                            class="calendar-day" 
                            :class="[day.class, {'has-profit': day.profit > 0, 'has-loss': day.profit < 0}]"
                            @click="handleDayClick(day)">
                            <div class="day-number">{{ day.day }}</div>
                            <div v-if="day.profit !== 0" class="day-profit" :class="getProfitClass(day.profit)">
                                <span v-if="profitDisplayMode === 'amount'">{{ formatProfit(day.profit) }}</span>
                                <span v-else>{{ formatProfitRate(day.profitRate) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="calendar-stats">
                        <div class="stat-card">
                            <div class="stat-icon">📈</div>
                            <div class="stat-content">
                                <div class="stat-value" :class="getProfitClass(profitCalendarData.totalProfit || 0)">
                                    {{ formatProfit(profitCalendarData.totalProfit || 0) }}
                                </div>
                                <div class="stat-label">总收益</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">📊</div>
                            <div class="stat-content">
                                <div class="stat-value">{{ getTradingDaysCount() }}</div>
                                <div class="stat-label">交易天数</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🎯</div>
                            <div class="stat-content">
                                <div class="stat-value profit-positive">{{ getProfitDaysCount() }}</div>
                                <div class="stat-label">盈利天数</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">💰</div>
                            <div class="stat-content">
                                <div class="stat-value">{{ getAverageDailyProfit() }}</div>
                                <div class="stat-label">日均收益</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>

        <!-- 交割单弹窗 -->
        <el-dialog 
            v-model="deliveryInfoVisible" 
            :width="getDeliveryDialogWidth()" 
            :before-close="handleCloseDeliveryInfo"
            class="delivery-info-dialog">
            <template #header>
                <div class="dialog-header">
                    <div class="dialog-title">
                        <i class="el-icon-document" style="margin-right: 8px;"></i>
                        <span>{{ selectedDeliveryAccountType }} - 交割单</span>
                    </div>
                    <div class="dialog-subtitle">
                        <span>共 {{ deliveryInfoList.length }} 条记录</span>
                    </div>
                </div>
            </template>
            
            <div class="delivery-info-content">
                <!-- 筛选器 -->
                <div class="delivery-filters">
                    <div class="filter-group">
                        <label>股票代码:</label>
                        <el-input 
                            v-model="deliveryFilters.symbol" 
                            placeholder="输入股票代码筛选"
                            size="small"
                            style="width: 120px;"
                            clearable>
                        </el-input>
                    </div>
                    <div class="filter-group">
                        <label>交易类型:</label>
                        <el-select 
                            v-model="deliveryFilters.tradeType" 
                            placeholder="选择交易类型"
                            size="small"
                            style="width: 140px;"
                            clearable>
                            <el-option v-for="tradeType in availableTradeTypes" 
                                       :key="tradeType" 
                                       :label="tradeType" 
                                       :value="tradeType">
                            </el-option>
                        </el-select>
                    </div>
                    <div class="filter-group">
                        <label>时间范围:</label>
                        <el-date-picker
                            v-model="deliveryFilters.dateRange"
                            type="daterange"
                            size="small"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            style="width: 200px;">
                        </el-date-picker>
                    </div>
                    <div class="filter-group">
                        <el-button type="primary" size="small" @click="applyDeliveryFilters">筛选</el-button>
                        <el-button size="small" @click="resetDeliveryFilters">重置</el-button>
                    </div>
                </div>
                
                <!-- 交割单表格 -->
                <el-table 
                    :data="filteredDeliveryList" 
                    border 
                    stripe
                    style="width: 100%"
                    :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
                    empty-text="暂无交割单记录"
                    max-height="600">
                    <el-table-column prop="createTime" label="交易时间" min-width="160" sortable>
                        <template #default="scope">
                            {{ formatDeliveryTime(scope.row.createTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="symbol" label="股票代码" min-width="100" align="center">
                        <template #default="scope">
                            <el-tag v-if="scope.row.symbol" size="small" type="info">{{ scope.row.symbol }}</el-tag>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="股票名称" min-width="120" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="tradeType" label="交易类型" min-width="110" align="center" sortable>
                        <template #default="scope">
                            <el-tag 
                                :type="getTradeTypeColor(scope.row.tradeType)"
                                size="small">
                                {{ scope.row.tradeType }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="tradePrice" label="交易价格" min-width="100" align="right" sortable>
                        <template #default="scope">
                            <span v-if="scope.row.tradePrice > 0">{{ scope.row.tradePrice.toFixed(3) }}</span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="tradeAmount" label="交易数量" min-width="100" align="right" sortable>
                        <template #default="scope">
                            <span v-if="scope.row.tradeAmount > 0">{{ scope.row.tradeAmount.toLocaleString() }}</span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="tradeGold" label="交易金额" min-width="120" align="right" sortable>
                        <template #default="scope">
                            <span style="font-weight: 600;">{{ formatMoney(scope.row.tradeGold) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="realTradeGold" label="实际金额" min-width="120" align="right" sortable>
                        <template #default="scope">
                            <span style="font-weight: 600;">{{ formatMoney(scope.row.realTradeGold) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="费用明细" min-width="300">
                        <template #default="scope">
                            <div class="fee-details">
                                <div class="fee-item">
                                    <span class="fee-label">佣金:</span>
                                    <span class="fee-value">{{ scope.row.commission.toFixed(2) }}</span>
                                </div>
                                <div class="fee-item">
                                    <span class="fee-label">印花税:</span>
                                    <span class="fee-value">{{ scope.row.stampDuty.toFixed(2) }}</span>
                                </div>
                                <div class="fee-item">
                                    <span class="fee-label">过户费:</span>
                                    <span class="fee-value">{{ scope.row.transferFee.toFixed(2) }}</span>
                                </div>
                                <div class="fee-item">
                                    <span class="fee-label">交易费:</span>
                                    <span class="fee-value">{{ scope.row.transactionFee.toFixed(2) }}</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="stockBalance" label="股票余额" min-width="100" align="right" sortable>
                        <template #default="scope">
                            <span v-if="scope.row.stockBalance > 0">{{ scope.row.stockBalance.toLocaleString() }}</span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="accountBalance" label="账户余额" min-width="120" align="right" sortable>
                        <template #default="scope">
                            <span style="font-weight: 600;">{{ formatMoney(scope.row.accountBalance) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="platformOrderId" label="订单ID" min-width="160" show-overflow-tooltip></el-table-column>
                </el-table>
            </div>
        </el-dialog>
    </div>

    <script type="text/babel">
        const { createApp, ref, reactive, onMounted } = Vue
        const { ElMessage, ElMessageBox } = ElementPlus

        const app = createApp({
            setup() {
                const accountsInfo = ref({})
                const stockSearchResults = ref([])
                const tradeResults = ref([])
                const selectedAccountDetail = ref(null)
                const accountDetailVisible = ref(false)
                
                // 交割单相关数据
                const deliveryInfoVisible = ref(false)
                const selectedDeliveryAccountType = ref('')
                const deliveryInfoList = ref([])
                const filteredDeliveryList = ref([])
                const availableTradeTypes = ref([])  // 动态交易类型列表
                const deliveryFilters = reactive({
                    symbol: '',
                    tradeType: '',
                    dateRange: null
                })

                // 收益日历相关
                const profitCalendarVisible = ref(false)
                const selectedProfitAccountType = ref('')
                const profitCalendarData = ref({})
                const calendarMonth = ref('')
                const calendarDays = ref([])
                const profitDisplayMode = ref('amount') // 'amount' or 'rate'

                // 总仓位
                const totalPosition = ref(85)

                // 密码锁相关
                const showPasswordLock = ref(true)
                const passwordInput = ref('')
                const passwordError = ref('')
                const passwordSuccess = ref('')
                const passwordChecking = ref(false)
                const passwordInputError = ref(false)
                // 使用简单编码的密码 (147852 -> base64编码)
                const correctPassword = atob('MTQ3ODUy')  // 147852的base64编码
                const lockTimeout = 30 * 60 * 1000  // 30分钟无操作自动锁定
                let activityTimer = null

                // 表单数据
                const selectedClearAccount = ref('')  // 选中的清仓账户
                const clearMsg = ref('一键清仓')
                const excludeStockSymbols = ref('')  // 要排除的股票代码
                const sellStockSymbol = ref('')
                const sellStockInfo = ref(null)  // 存储选中的卖出股票完整信息
                const sellPercent = ref(100)
                const sellMsg = ref('一键卖出')
                const buyStockSymbol = ref('')
                const buyStockInfo = ref(null)   // 存储选中的买入股票完整信息
                const buyPercent = ref(10)
                const buyMsg = ref('一键买入')

                // 加载状态
                const loading = reactive({
                    clear: false,
                    sell: false,
                    buy: false,
                    position: false,
                    profitCalendar: false,
                })
                const positionLoading = reactive({
                    buy: {},
                    sell: {}
                })
                const statusLoading = reactive({})
                const showLoading = ref(false)
                const loadingText = ref('处理中...')

                // 格式化金额
                const formatMoney = (amount) => {
                    if (!amount) return '0.00'
                    return (amount / 10000).toFixed(2) + '万'
                }

                const formatMoneyColumn = (row, column, cellValue) => {
                    return formatMoney(cellValue)
                }

                // 解析排除股票代码
                const parseExcludeSymbols = () => {
                    if (!excludeStockSymbols.value || !excludeStockSymbols.value.trim()) {
                        return []
                    }
                    
                    // 支持逗号、空格、换行分隔
                    return excludeStockSymbols.value
                        .split(/[,\s\n]+/)  // 使用正则表达式分割逗号、空格、换行
                        .map(symbol => symbol.trim().toUpperCase())  // 去除空格并转为大写
                        .filter(symbol => symbol.length > 0)  // 过滤空字符串
                }

                // 格式化盈亏为数字格式（如 1,000.00）
                const formatProfit = (amount) => {
                    if (!amount) return '0.00'
                    
                    // 绝对值用于判断数值大小
                    const absAmount = Math.abs(amount)
                    
                    // 百万以上显示为万元格式
                    if (absAmount >= 1000000) {
                        return (amount / 10000).toFixed(2) + '万'
                    }
                    // 万元以下显示为数字格式
                    else {
                        return amount.toLocaleString('zh-CN', { 
                            minimumFractionDigits: 2, 
                            maximumFractionDigits: 2 
                        })
                    }
                }

                const formatProfitColumn = (row, column, cellValue) => {
                    return formatProfit(cellValue)
                }

                // 获取盈亏颜色样式
                const getProfitClass = (profit) => {
                    if (profit > 0) return 'profit-positive'
                    if (profit < 0) return 'profit-negative'
                    return 'profit-zero'
                }

                // 密码验证
                const checkPassword = async () => {
                    if (!passwordInput.value.trim()) {
                        passwordError.value = '请输入密码'
                        passwordInputError.value = true
                        setTimeout(() => { passwordInputError.value = false }, 500)
                        return
                    }

                    passwordChecking.value = true
                    passwordError.value = ''
                    passwordSuccess.value = ''
                    passwordInputError.value = false

                    try {
                        // 模拟验证延迟，提供更好的用户体验
                        await new Promise(resolve => setTimeout(resolve, 300))
                        
                        if (passwordInput.value === correctPassword) {
                            passwordSuccess.value = '密码正确，正在解锁...'
                            setTimeout(() => {
                                showPasswordLock.value = false
                                passwordError.value = ''
                                passwordSuccess.value = ''
                                passwordInput.value = ''
                                passwordChecking.value = false
                                startActivityTimer()
                            }, 500)
                        } else {
                            passwordError.value = '❌ 密码错误，请重试'
                            passwordInputError.value = true
                            passwordInput.value = ''
                            passwordChecking.value = false
                            
                            // 清除错误状态
                            setTimeout(() => { 
                                passwordInputError.value = false 
                                passwordError.value = ''
                            }, 2000)
                        }
                    } catch (error) {
                        console.error('密码验证错误:', error)
                        passwordError.value = '验证失败，请重试'
                        passwordChecking.value = false
                        passwordInputError.value = true
                        setTimeout(() => { passwordInputError.value = false }, 500)
                    }
                }

                // 启动活动计时器
                const startActivityTimer = () => {
                    clearTimeout(activityTimer)
                    activityTimer = setTimeout(() => {
                        showPasswordLock.value = true
                        passwordError.value = ''
                    }, lockTimeout)
                }

                // 重置活动计时器
                const resetActivityTimer = () => {
                    if (!showPasswordLock.value) {
                        startActivityTimer()
                    }
                }

                // 锁定页面
                const lockPage = () => {
                    showPasswordLock.value = true
                    passwordError.value = ''
                }
                
                // 跳转到监控打板页面
                const goToMonitorPage = () => {
                    window.open('/stock_monitor_daban.html', '_blank')
                }
                
                // 跳转到策略交易页面
                const goToStrategyTrade = () => {
                    window.open('/stock_strategy_trade.html', '_blank')
                }

                // 获取账户信息
                const refreshAccountInfo = async () => {
                    try {
                        const response = await fetch('/api/oneclick_trade/accounts')
                        const data = await response.json()
                        if (data.status === 'success') {
                            accountsInfo.value = data.accounts
                        }
                    } catch (error) {
                        console.error('获取账户信息失败:', error)
                        ElMessage.error('获取账户信息失败')
                    }
                }

                // 搜索股票
                const searchStock = async () => {
                    const keyword = sellStockSymbol.value || buyStockSymbol.value
                    if (!keyword || keyword.length < 2) {
                        stockSearchResults.value = []
                        return
                    }

                    try {
                        const response = await fetch(`/api/oneclick_trade/search_stock?keyword=${encodeURIComponent(keyword)}`)
                        const data = await response.json()
                        stockSearchResults.value = data
                    } catch (error) {
                        console.error('搜索股票失败:', error)
                        stockSearchResults.value = []
                    }
                }

                // 选择股票
                const selectStock = (stock, type) => {
                    if (type === 'sell') {
                        sellStockSymbol.value = stock.symbol
                        sellStockInfo.value = stock  // 保存完整的股票信息
                    } else {
                        buyStockSymbol.value = stock.symbol
                        buyStockInfo.value = stock   // 保存完整的股票信息
                    }
                    stockSearchResults.value = []
                }

                // 查看账户详情
                const viewAccountDetail = async (accountType) => {
                    try {
                        const response = await fetch(`/api/oneclick_trade/account_detail?account_type=${accountType}`)
                        const data = await response.json()
                        if (data.status === 'success') {
                            selectedAccountDetail.value = data
                            accountDetailVisible.value = true
                        } else {
                            ElMessage.error(data.message || '获取账户详情失败')
                        }
                    } catch (error) {
                        console.error('获取账户详情失败:', error)
                        ElMessage.error('获取账户详情失败')
                    }
                }

                // 清仓单个账户
                const clearSingleAccount = async (accountType) => {
                    try {
                        await ElMessageBox.confirm(`确定要清仓账户 ${accountType} 的所有持仓吗？`, '确认清仓', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        })

                        showLoading.value = true
                        loadingText.value = `正在清仓账户 ${accountType}...`

                        const response = await fetch('/api/oneclick_trade/clear_single', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                account_type: accountType,
                                msg: `一键清仓账户-${accountType}`
                            })
                        })

                        const result = await response.json()
                        addTradeResult(`清仓账户 ${accountType}`, result)

                        if (result.status === 'success') {
                            ElMessage.success(`账户 ${accountType} 清仓操作已提交`)
                            // 后端已先关闭账户，前端同步更新状态
                            if (accountsInfo.value[accountType]) {
                                accountsInfo.value[accountType].status = 'close';
                            }
                            refreshAccountInfo()
                        } else {
                            ElMessage.error(result.message || '清仓操作失败')
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('清仓账户失败:', error)
                            ElMessage.error('清仓账户失败')
                        }
                    } finally {
                        showLoading.value = false
                    }
                }

                const toggleAccountStatus = async (accountType, currentStatus) => {
                    const newStatus = currentStatus === 'open' ? 'close' : 'open';
                    const actionText = newStatus === 'open' ? '启用' : '关闭';

                    try {
                        // 仅在关闭账户时弹出二次确认框
                        if (newStatus === 'close') {
                            await ElMessageBox.confirm(`确定要 ${actionText} 账户 ${accountType} 吗？`, `确认${actionText}`, {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                            });
                        }

                        statusLoading[accountType] = true;

                        const response = await fetch('/api/oneclick_trade/toggle_account_status', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                account_type: accountType,
                                status: newStatus,
                            }),
                        });

                        const result = await response.json();

                        if (result.status === 'success') {
                            ElMessage.success(`账户 ${accountType} 已成功${actionText}`);
                            if (accountsInfo.value[accountType]) {
                                accountsInfo.value[accountType].status = newStatus;
                            }
                        } else {
                            ElMessage.error(result.message || '操作失败');
                        }
                    } catch (error) {
                        // 如果用户点击了取消，ElMessageBox会抛出'cancel'的error
                        if (error !== 'cancel') {
                            console.error('切换账户状态失败:', error);
                            ElMessage.error('操作失败');
                        }
                    } finally {
                        statusLoading[accountType] = false;
                    }
                };

                // 获取清仓按钮文本
                const getClearButtonText = () => {
                    if (!selectedClearAccount.value) {
                        return '请选择账户'
                    }
                    if (selectedClearAccount.value === 'ALL_ACCOUNTS') {
                        return '🔥 确认清仓所有账户'
                    }
                    return `清仓账户 ${selectedClearAccount.value}`
                }

                // 执行清仓操作
                const executeClearOperation = async () => {
                    if (!selectedClearAccount.value) {
                        ElMessage.error('请选择要清仓的账户')
                        return
                    }

                    if (selectedClearAccount.value === 'ALL_ACCOUNTS') {
                        await clearAllAccounts()
                    } else {
                        await clearSelectedAccount(selectedClearAccount.value)
                    }
                }

                // 清仓选中的单个账户
                const clearSelectedAccount = async (accountType) => {
                    try {
                        await ElMessageBox.confirm(`确定要清仓账户 ${accountType} 的所有持仓吗？`, '确认清仓', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        })

                        loading.clear = true

                        const response = await fetch('/api/oneclick_trade/clear_single', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                account_type: accountType,
                                msg: clearMsg.value,
                                exclude_symbols: parseExcludeSymbols()
                            })
                        })

                        const result = await response.json()
                        addTradeResult(`清仓账户 ${accountType}`, result)

                        if (result.status === 'success') {
                            ElMessage.success(`账户 ${accountType} 清仓操作已提交`)
                            // 后端已先关闭账户，前端同步更新状态
                            if (accountsInfo.value[accountType]) {
                                accountsInfo.value[accountType].status = 'close';
                            }
                            refreshAccountInfo()
                            selectedClearAccount.value = ''  // 重置选择
                        } else {
                            ElMessage.error(result.message || '清仓操作失败')
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('清仓账户失败:', error)
                            ElMessage.error('清仓账户失败')
                        }
                    } finally {
                        loading.clear = false
                    }
                }

                // 清仓所有账户
                const clearAllAccounts = async () => {
                    try {
                        await ElMessageBox.confirm('确定要清仓所有账户的所有持仓吗？这是不可逆操作！', '危险操作确认', {
                            confirmButtonText: '确定清仓',
                            cancelButtonText: '取消',
                            type: 'error'
                        })

                        loading.clear = true
                        const response = await fetch('/api/oneclick_trade/clear_all', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                msg: clearMsg.value,
                                exclude_symbols: parseExcludeSymbols()
                            })
                        })

                        const result = await response.json()
                        addTradeResult('清仓所有账户', result)

                        if (result.status !== 'error') {
                            ElMessage.success('清仓操作已提交')
                            // 后端已先关闭所有账户，前端同步更新状态
                            for (const accType in accountsInfo.value) {
                                if (accountsInfo.value.hasOwnProperty(accType)) {
                                    accountsInfo.value[accType].status = 'close';
                                }
                            }
                            refreshAccountInfo()
                            selectedClearAccount.value = ''  // 重置选择
                        } else {
                            ElMessage.error(result.message || '清仓操作失败')
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('清仓所有账户失败:', error)
                            ElMessage.error('清仓所有账户失败')
                        }
                    } finally {
                        loading.clear = false
                    }
                }

                // 卖出股票
                const sellStock = async () => {
                    console.log('🔥 sellStock 函数被调用了！')
                    console.log('sellStockSymbol.value:', sellStockSymbol.value)
                    console.log('sellPercent.value:', sellPercent.value)
                    
                    if (!sellStockSymbol.value) {
                        console.log('❌ 股票代码为空，显示错误消息')
                        ElMessage.error('请选择要卖出的股票')
                        return
                    }

                    console.log('✅ 开始显示确认对话框')
                    try {
                        // 获取股票显示名称
                        const stockDisplayName = sellStockInfo.value ? 
                            `${sellStockInfo.value.symbol} (${sellStockInfo.value.name})` : 
                            sellStockSymbol.value
                        
                        await ElMessageBox.confirm(`确定要卖出所有账户中的 ${stockDisplayName} (${sellPercent.value}%) 吗？`, '确认卖出', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        })
                        console.log('✅ 用户确认了卖出操作')

                        console.log('🚀 开始发送卖出请求')
                        loading.sell = true
                        
                        const requestData = {
                            symbol: sellStockSymbol.value,
                            pos_percent: sellPercent.value / 100,
                            msg: sellMsg.value
                        }
                        console.log('📤 请求数据:', requestData)
                        
                        const response = await fetch('/api/oneclick_trade/sell_stock', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(requestData)
                        })
                        
                        console.log('📥 收到响应:', response.status, response.statusText)
                        const result = await response.json()
                        console.log('📋 响应数据:', result)
                        
                        addTradeResult(`卖出 ${sellStockSymbol.value} (${sellPercent.value}%)`, result)

                        if (result.status !== 'error') {
                            ElMessage.success('卖出操作已提交')
                            refreshAccountInfo()
                        } else {
                            ElMessage.error(result.message || '卖出操作失败')
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('❌ 卖出股票失败:', error)
                            ElMessage.error('卖出股票失败')
                        } else {
                            console.log('ℹ️ 用户取消了卖出操作')
                        }
                    } finally {
                        console.log('🏁 卖出操作结束，重置loading状态')
                        loading.sell = false
                    }
                }

                // 买入股票
                const buyStock = async () => {
                    if (!buyStockSymbol.value) {
                        ElMessage.error('请选择要买入的股票')
                        return
                    }

                    try {
                        // 获取股票显示名称
                        const stockDisplayName = buyStockInfo.value ? 
                            `${buyStockInfo.value.symbol} (${buyStockInfo.value.name})` : 
                            buyStockSymbol.value
                        const percentValue = buyPercent.value
                        
                        await ElMessageBox.confirm(`确定要买入 ${stockDisplayName} (${percentValue}% 资金) 吗？`, '确认买入', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'info'
                        })

                        loading.buy = true
                        const response = await fetch('/api/oneclick_trade/buy_stock', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                symbol: buyStockSymbol.value,
                                pos_percent: buyPercent.value / 100,
                                msg: buyMsg.value
                            })
                        })

                        const result = await response.json()
                        addTradeResult(`买入 ${buyStockSymbol.value} (${buyPercent.value}%)`, result)

                        if (result.status !== 'error') {
                            ElMessage.success('买入操作已提交')
                            refreshAccountInfo()
                        } else {
                            ElMessage.error(result.message || '买入操作失败')
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('买入股票失败:', error)
                            ElMessage.error('买入股票失败')
                        }
                    } finally {
                        loading.buy = false
                    }
                }

                // 从持仓买入股票
                const buyStockFromPosition = async (position) => {
                    const symbol = position.symbol
                    const stockName = position.name

                    try {
                        // 弹出输入对话框询问买入金额
                        const { value: buyAmount } = await ElMessageBox.prompt(
                            `请输入买入 ${symbol} (${stockName}) 的金额（元）`,
                            '买入股票',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                inputPattern: /^[1-9]\d*$/,
                                inputErrorMessage: '请输入大于0的整数金额',
                                inputValue: '1000'
                            }
                        )

                        const amountValue = parseInt(buyAmount)
                        
                        if (amountValue < 100) {
                            ElMessage.error('买入金额不能少于100元')
                            return
                        }
                        
                        await ElMessageBox.confirm(
                            `确定要买入 ${symbol} (${stockName}) ${amountValue} 元吗？`, 
                            '确认买入', 
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'info'
                            }
                        )

                        // 设置loading状态
                        if (!positionLoading.buy[symbol]) {
                            positionLoading.buy[symbol] = false
                        }
                        positionLoading.buy[symbol] = true

                        const response = await fetch('/api/oneclick_trade/buy_stock_single', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                account_type: selectedAccountDetail.value.account_type,
                                symbol: symbol,
                                buy_amount: amountValue,
                                msg: `持仓买入-${symbol}`
                            })
                        })

                        const result = await response.json()
                        addTradeResult(`买入 ${symbol} (${amountValue}元)`, result)

                        if (result.status !== 'error') {
                            ElMessage.success(`买入 ${symbol} 操作已提交`)
                            refreshAccountInfo()
                            // 刷新账户详情
                            if (selectedAccountDetail.value) {
                                setTimeout(() => {
                                    viewAccountDetail(selectedAccountDetail.value.account_type)
                                }, 1000)
                            }
                        } else {
                            ElMessage.error(result.message || '买入操作失败')
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('买入股票失败:', error)
                            ElMessage.error('买入股票失败')
                        }
                    } finally {
                        positionLoading.buy[symbol] = false
                    }
                }

                // 从持仓卖出股票
                const sellStockFromPosition = async (position) => {
                    const symbol = position.symbol
                    const stockName = position.name
                    const quantity = position.quantity
                    const availableAmount = position.availableAmount

                    try {
                        // 弹出输入对话框询问卖出股数
                        const { value: sellQuantity } = await ElMessageBox.prompt(
                            `请输入卖出 ${symbol} (${stockName}) 的股数\n当前持股: ${quantity} 股\n可卖数量: ${availableAmount} 股`,
                            '卖出股票',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                inputPattern: /^[1-9]\d*$/,
                                inputErrorMessage: '请输入大于0的整数股数',
                                inputValue: availableAmount.toString()
                            }
                        )

                        const sellQuantityValue = parseInt(sellQuantity)
                        
                        if (sellQuantityValue > availableAmount) {
                            ElMessage.error(`卖出股数不能超过可卖数量 ${availableAmount} 股`)
                            return
                        }
                        
                        await ElMessageBox.confirm(
                            `确定要卖出 ${symbol} (${stockName}) ${sellQuantityValue} 股吗？`, 
                            '确认卖出', 
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        )

                        // 设置loading状态
                        if (!positionLoading.sell[symbol]) {
                            positionLoading.sell[symbol] = false
                        }
                        positionLoading.sell[symbol] = true

                        const response = await fetch('/api/oneclick_trade/sell_stock_single', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                account_type: selectedAccountDetail.value.account_type,
                                symbol: symbol,
                                sell_quantity: sellQuantityValue,
                                msg: `持仓卖出-${symbol}`
                            })
                        })

                        const result = await response.json()
                        addTradeResult(`卖出 ${symbol} (${sellQuantityValue}股)`, result)

                        if (result.status !== 'error') {
                            ElMessage.success(`卖出 ${symbol} 操作已提交`)
                            refreshAccountInfo()
                            // 刷新账户详情
                            if (selectedAccountDetail.value) {
                                setTimeout(() => {
                                    viewAccountDetail(selectedAccountDetail.value.account_type)
                                }, 1000)
                            }
                        } else {
                            ElMessage.error(result.message || '卖出操作失败')
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('卖出股票失败:', error)
                            ElMessage.error('卖出股票失败')
                        }
                    } finally {
                        positionLoading.sell[symbol] = false
                    }
                }

                // 添加交易结果
                const addTradeResult = (title, data) => {
                    tradeResults.value.unshift({
                        title,
                        data,
                        time: new Date().toLocaleString()
                    })
                    // 只保留最近10条记录
                    if (tradeResults.value.length > 10) {
                        tradeResults.value = tradeResults.value.slice(0, 10)
                    }
                }

                // 关闭账户详情弹窗
                const handleCloseAccountDetail = (done) => {
                    done()
                }

                // 获取表格行样式
                const getRowClassName = ({ row }) => {
                    if (row.profit > 0) {
                        return 'profit-row'
                    } else if (row.profit < 0) {
                        return 'loss-row'
                    }
                    return ''
                }

                // 获取进度条颜色
                const getProgressColor = (percentage) => {
                    if (percentage > 0.3) {
                        return '#f56c6c'
                    } else if (percentage > 0.15) {
                        return '#e6a23c'
                    } else {
                        return '#409eff'
                    }
                }

                // 查看交割单信息
                const viewDeliveryInfo = async (accountType) => {
                    try {
                        selectedDeliveryAccountType.value = accountType
                        deliveryInfoVisible.value = true
                        
                        const response = await fetch(`/api/oneclick_trade/delivery_info?account_type=${accountType}`)
                        const data = await response.json()
                        
                        if (data.status === 'success') {
                            deliveryInfoList.value = data.data
                            filteredDeliveryList.value = data.data
                            // 更新可用的交易类型列表
                            updateAvailableTradeTypes(data.data)
                        } else {
                            ElMessage.error(data.message || '获取交割单信息失败')
                            deliveryInfoList.value = []
                            filteredDeliveryList.value = []
                            availableTradeTypes.value = []
                        }
                    } catch (error) {
                        console.error('获取交割单信息失败:', error)
                        ElMessage.error('获取交割单信息失败')
                        deliveryInfoList.value = []
                        filteredDeliveryList.value = []
                        availableTradeTypes.value = []
                    }
                }

                // 关闭交割单弹窗
                const handleCloseDeliveryInfo = (done) => {
                    deliveryInfoList.value = []
                    filteredDeliveryList.value = []
                    availableTradeTypes.value = []
                    selectedDeliveryAccountType.value = ''
                    resetDeliveryFilters()
                    done()
                }

                // 格式化交割单时间
                const formatDeliveryTime = (timeStr) => {
                    if (!timeStr || timeStr.length < 14) return timeStr
                    // 格式: ************** -> 2025-07-15 14:58:20
                    const year = timeStr.substr(0, 4)
                    const month = timeStr.substr(4, 2)
                    const day = timeStr.substr(6, 2)
                    const hour = timeStr.substr(8, 2)
                    const minute = timeStr.substr(10, 2)
                    const second = timeStr.substr(12, 2)
                    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
                }

                // 获取交易类型颜色
                const getTradeTypeColor = (tradeType) => {
                    switch (tradeType) {
                        case '证券买入':
                            return 'success'
                        case '证券卖出':
                            return 'danger'
                        case '天天宝申购':
                            return 'primary'
                        case '天天宝赎回':
                            return 'warning'
                        default:
                            return 'info'
                    }
                }

                // 更新可用的交易类型列表
                const updateAvailableTradeTypes = (deliveryData) => {
                    const tradeTypes = new Set()
                    deliveryData.forEach(item => {
                        if (item.tradeType && item.tradeType.trim()) {
                            tradeTypes.add(item.tradeType.trim())
                        }
                    })
                    availableTradeTypes.value = Array.from(tradeTypes).sort()
                }

                // 获取交割单弹窗的响应式宽度
                const getDeliveryDialogWidth = () => {
                    const screenWidth = window.innerWidth
                    if (screenWidth <= 768) {
                        return '95%'  // 移动端
                    } else if (screenWidth <= 1200) {
                        return '90%'  // 平板
                    } else if (screenWidth <= 1600) {
                        return '85%'  // 小屏幕桌面
                    } else {
                        return '80%'  // 大屏幕桌面
                    }
                }

                // 应用筛选条件
                const applyDeliveryFilters = () => {
                    let filtered = [...deliveryInfoList.value]
                    
                    // 股票代码筛选
                    if (deliveryFilters.symbol) {
                        filtered = filtered.filter(item => 
                            item.symbol && item.symbol.toLowerCase().includes(deliveryFilters.symbol.toLowerCase())
                        )
                    }
                    
                    // 交易类型筛选
                    if (deliveryFilters.tradeType) {
                        filtered = filtered.filter(item => item.tradeType === deliveryFilters.tradeType)
                    }
                    
                    // 时间范围筛选
                    if (deliveryFilters.dateRange && deliveryFilters.dateRange.length === 2) {
                        const startDate = deliveryFilters.dateRange[0].replace(/-/g, '')
                        const endDate = deliveryFilters.dateRange[1].replace(/-/g, '')
                        filtered = filtered.filter(item => {
                            const itemDate = item.createTime.substr(0, 8)
                            return itemDate >= startDate && itemDate <= endDate
                        })
                    }
                    
                    filteredDeliveryList.value = filtered
                }

                // 重置筛选条件
                const resetDeliveryFilters = () => {
                    deliveryFilters.symbol = ''
                    deliveryFilters.tradeType = ''
                    deliveryFilters.dateRange = null
                    filteredDeliveryList.value = [...deliveryInfoList.value]
                }

                // 获取总仓位
                const getTotalPosition = async () => {
                    try {
                        const response = await fetch('/api/oneclick_trade/total_position')
                        const data = await response.json()
                        if (data.status === 'success' && data.position) {
                            totalPosition.value = parseFloat((data.position * 100).toFixed(2))
                        }
                    } catch (error) {
                        console.error('获取总仓位失败:', error)
                        ElMessage.error('获取总仓位设置失败')
                    }
                }

                // 保存总仓位
                const saveTotalPosition = async () => {
                    loading.position = true
                    try {
                        const response = await fetch('/api/oneclick_trade/total_position', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                position: totalPosition.value / 100
                            })
                        })
                        const result = await response.json()
                        if (result.status === 'success') {
                            ElMessage.success('总仓位保存成功')
                        } else {
                            ElMessage.error(result.message || '保存总仓位失败')
                        }
                    } catch (error) {
                        console.error('保存总仓位失败:', error)
                        ElMessage.error('保存总仓位失败')
                    } finally {
                        loading.position = false
                    }
                }

                // ---------------- 收益日历相关函数 ----------------

                // 查看收益日历
                const viewProfitCalendar = async (accountType) => {
                    selectedProfitAccountType.value = accountType
                    profitCalendarVisible.value = true
                    calendarMonth.value = new Date().toISOString().slice(0, 7) // 格式 YYYY-MM
                    await fetchProfitCalendarData()
                }

                // 获取收益日历数据
                const fetchProfitCalendarData = async () => {
                    if (!selectedProfitAccountType.value || !calendarMonth.value) return;
                    
                    loading.profitCalendar = true;
                    try {
                        const response = await fetch(`/api/oneclick_trade/profit_info?account_type=${selectedProfitAccountType.value}&month=${calendarMonth.value}`)
                        const result = await response.json()
                        if (result.status === 'success') {
                            profitCalendarData.value = result.data
                        } else {
                            profitCalendarData.value = {}
                            ElMessage.error(result.message || '获取收益数据失败')
                        }
                    } catch (error) {
                        console.error('获取收益数据失败:', error)
                        ElMessage.error('获取收益数据失败')
                    } finally {
                        loading.profitCalendar = false
                        generateCalendar()
                    }
                }

                // 生成日历
                const generateCalendar = () => {
                    if (!calendarMonth.value) return;

                    const [year, month] = calendarMonth.value.split('-').map(Number)
                    const profitMap = new Map()
                    
                    // 构建收益数据映射
                    if (profitCalendarData.value.profitInfoGroup && profitCalendarData.value.profitInfoGroup.length > 0) {
                        profitCalendarData.value.profitInfoGroup.forEach(item => {
                            // tradeDate是YYYYMMDD格式
                            const day = parseInt(item.tradeDate.slice(6, 8), 10)
                            profitMap.set(day, { 
                                profit: parseFloat(item.profit || 0), 
                                profitRate: parseFloat(item.profitRate || 0) 
                            })
                        })
                    }

                    const days = []
                    const firstDayOfMonth = new Date(year, month - 1, 1)
                    const firstDayOfWeek = (firstDayOfMonth.getDay() + 6) % 7 // 0=Mon, 1=Tue, ..., 6=Sun
                    const daysInMonth = new Date(year, month, 0).getDate()
                    const today = new Date()
                    const todayStr = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`

                    // 计算需要显示的上个月的天数（只显示工作日）
                    const prevMonthDays = []
                    for (let i = firstDayOfWeek; i > 0; i--) {
                        const prevDate = new Date(year, month - 1, 1 - i)
                        if (prevDate.getDay() !== 6 && prevDate.getDay() !== 0) { // 过滤周六日
                            prevMonthDays.push({
                                day: prevDate.getDate(),
                                profit: 0,
                                profitRate: 0,
                                fullDate: prevDate.toISOString().slice(0, 10),
                                class: 'is-other-month'
                            })
                        }
                    }
                    days.push(...prevMonthDays)

                    // 当前月的日子（只显示工作日）
                    for (let i = 1; i <= daysInMonth; i++) {
                        const currentDate = new Date(year, month - 1, i)
                        if (currentDate.getDay() !== 6 && currentDate.getDay() !== 0) { // 过滤周六日
                            const fullDate = `${year}-${month.toString().padStart(2, '0')}-${i.toString().padStart(2, '0')}`
                            const profitData = profitMap.get(i) || { profit: 0, profitRate: 0 }
                            days.push({ 
                                day: i,
                                profit: profitData.profit,
                                profitRate: profitData.profitRate,
                                fullDate: fullDate,
                                class: fullDate === todayStr ? 'is-today' : ''
                            })
                        }
                    }

                    // 下个月的开头，补齐到5的倍数（只显示工作日）
                    let nextDay = 1
                    while (days.length % 5 !== 0) {
                        const nextDate = new Date(year, month, nextDay)
                        if (nextDate.getDay() !== 6 && nextDate.getDay() !== 0) { // 过滤周六日
                            days.push({
                                day: nextDate.getDate(),
                                profit: 0,
                                profitRate: 0,
                                fullDate: nextDate.toISOString().slice(0, 10),
                                class: 'is-other-month'
                            })
                        }
                        nextDay++
                    }

                    calendarDays.value = days
                    console.log('生成日历数据:', days.length, '天')
                }

                const formatCalendarTitle = (monthStr) => {
                    if (!monthStr) return ''
                    const [year, month] = monthStr.split('-')
                    return `${year}年${month}月`
                }

                // 格式化收益率
                const formatProfitRate = (rate) => {
                    if (typeof rate !== 'number') return '0.00%';
                    return `${rate.toFixed(2)}%`;
                }

                // 月份改变
                const handleMonthChange = async () => {
                    await fetchProfitCalendarData()
                }

                // 关闭收益日历
                const handleCloseProfitCalendar = (done) => {
                    profitCalendarData.value = {}
                    calendarDays.value = []
                    selectedProfitAccountType.value = ''
                    done()
                }

                // 处理日历日期点击
                const handleDayClick = (day) => {
                    if (day.class === 'is-other-month') return
                    
                    if (day.profit !== 0) {
                        const profitText = profitDisplayMode.value === 'amount' ? 
                            formatProfit(day.profit) : 
                            formatProfitRate(day.profitRate)
                        
                        ElMessage({
                            message: `${day.fullDate} 收益: ${profitText}`,
                            type: day.profit > 0 ? 'success' : 'error',
                            duration: 3000
                        })
                    } else {
                        ElMessage({
                            message: `${day.fullDate} 无交易记录`,
                            type: 'info',
                            duration: 2000
                        })
                    }
                }

                // 获取当前交易时间状态
                const getTradingTimeStatus = () => {
                    const now = new Date()
                    const currentTime = now.getTime()
                    const hour = now.getHours()
                    const minute = now.getMinutes()
                    const weekday = now.getDay()
                    
                    // 是否是工作日 (周一到周五)
                    const isWeekday = weekday >= 1 && weekday <= 5
                    
                    // 交易时间段检查
                    const morningStart = 9 * 60 + 30  // 9:30
                    const morningEnd = 11 * 60 + 30   // 11:30
                    const afternoonStart = 13 * 60    // 13:00
                    const afternoonEnd = 15 * 60      // 15:00
                    
                    const currentMinutes = hour * 60 + minute
                    
                    const isTradingTime = isWeekday && (
                        (currentMinutes >= morningStart && currentMinutes <= morningEnd) ||
                        (currentMinutes >= afternoonStart && currentMinutes <= afternoonEnd)
                    )
                    
                    return {
                        isWeekday,
                        isTradingTime,
                        currentTime: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
                        status: isTradingTime ? '交易时间' : '非交易时间'
                    }
                }

                // 格式化数字为千分位
                const formatNumber = (num) => {
                    if (typeof num !== 'number') return '0'
                    return num.toLocaleString('zh-CN', { 
                        minimumFractionDigits: 2, 
                        maximumFractionDigits: 2 
                    })
                }

                // 验证股票代码格式
                const validateStockSymbol = (symbol) => {
                    if (!symbol) return false
                    // 简单的股票代码验证：6位数字
                    return /^\d{6}$/.test(symbol.trim())
                }

                // 显示操作确认对话框
                const showConfirmDialog = async (title, content, type = 'warning') => {
                    try {
                        await ElMessageBox.confirm(content, title, {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: type,
                            customClass: 'confirm-dialog'
                        })
                        return true
                    } catch (error) {
                        return false
                    }
                }

                // 获取交易天数
                const getTradingDaysCount = () => {
                    if (!profitCalendarData.value.profitInfoGroup) return 0
                    return profitCalendarData.value.profitInfoGroup.length
                }

                // 获取盈利天数
                const getProfitDaysCount = () => {
                    if (!profitCalendarData.value.profitInfoGroup) return 0
                    return profitCalendarData.value.profitInfoGroup.filter(day => day.profit > 0).length
                }

                // 获取日均收益
                const getAverageDailyProfit = () => {
                    if (!profitCalendarData.value.profitInfoGroup || profitCalendarData.value.profitInfoGroup.length === 0) {
                        return '0.00'
                    }
                    const totalProfit = profitCalendarData.value.totalProfit || 0
                    const tradingDays = profitCalendarData.value.profitInfoGroup.length
                    const avgProfit = totalProfit / tradingDays
                    return formatProfit(avgProfit)
                }

                // 获取胜率
                const getWinRate = () => {
                    if (!profitCalendarData.value.profitInfoGroup || profitCalendarData.value.profitInfoGroup.length === 0) {
                        return '0%'
                    }
                    const totalDays = profitCalendarData.value.profitInfoGroup.length
                    const profitDays = profitCalendarData.value.profitInfoGroup.filter(day => day.profit > 0).length
                    const winRate = (profitDays / totalDays * 100).toFixed(1)
                    return `${winRate}%`
                }

                // 获取最大单日收益
                const getMaxDailyProfit = () => {
                    if (!profitCalendarData.value.profitInfoGroup || profitCalendarData.value.profitInfoGroup.length === 0) {
                        return '0.00'
                    }
                    const maxProfit = Math.max(...profitCalendarData.value.profitInfoGroup.map(day => day.profit))
                    return formatProfit(maxProfit)
                }

                // 获取最大单日亏损
                const getMaxDailyLoss = () => {
                    if (!profitCalendarData.value.profitInfoGroup || profitCalendarData.value.profitInfoGroup.length === 0) {
                        return '0.00'
                    }
                    const minProfit = Math.min(...profitCalendarData.value.profitInfoGroup.map(day => day.profit))
                    return formatProfit(minProfit)
                }

                // 初始化
                onMounted(() => {
                    refreshAccountInfo()
                    getTotalPosition()
                    
                    // 添加活动监听器
                    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
                    events.forEach(event => {
                        document.addEventListener(event, resetActivityTimer, true)
                    })
                    
                    // 页面获得焦点时自动聚焦密码输入框
                    document.addEventListener('visibilitychange', () => {
                        if (!document.hidden && showPasswordLock.value) {
                            setTimeout(() => {
                                const passwordInputElement = document.querySelector('.password-input')
                                if (passwordInputElement) {
                                    passwordInputElement.focus()
                                }
                            }, 100)
                        }
                    })
                })

                return {
                    accountsInfo,
                    stockSearchResults,
                    tradeResults,
                    selectedAccountDetail,
                    accountDetailVisible,
                    // 交割单相关
                    deliveryInfoVisible,
                    selectedDeliveryAccountType,
                    deliveryInfoList,
                    filteredDeliveryList,
                    availableTradeTypes,
                    deliveryFilters,
                    // 密码锁相关
                    showPasswordLock,
                    passwordInput,
                    passwordError,
                    passwordSuccess,
                    passwordChecking,
                    passwordInputError,
                    checkPassword,
                    lockPage,
                    goToMonitorPage,
                    goToStrategyTrade,
                    selectedClearAccount,
                    clearMsg,
                    excludeStockSymbols,
                    sellStockSymbol,
                    sellStockInfo,
                    sellPercent,
                    sellMsg,
                    buyStockSymbol,
                    buyStockInfo,
                    buyPercent,
                    buyMsg,
                    loading,
                    positionLoading,
                    statusLoading,
                    showLoading,
                    loadingText,
                    formatMoney,
                    formatMoneyColumn,
                    parseExcludeSymbols,
                    formatProfit,
                    formatProfitColumn,
                    getProfitClass,
                    getClearButtonText,
                    refreshAccountInfo,
                    searchStock,
                    selectStock,
                    viewAccountDetail,
                    clearSingleAccount,
                    executeClearOperation,
                    clearSelectedAccount,
                    clearAllAccounts,
                    toggleAccountStatus,
                    sellStock,
                    buyStock,
                    buyStockFromPosition,
                    sellStockFromPosition,
                    handleCloseAccountDetail,
                    getRowClassName,
                    getProgressColor,
                    // 交割单相关函数
                    viewDeliveryInfo,
                    handleCloseDeliveryInfo,
                    formatDeliveryTime,
                    getTradeTypeColor,
                    getDeliveryDialogWidth,
                    applyDeliveryFilters,
                    resetDeliveryFilters,
                    totalPosition,
                    saveTotalPosition,
                    // 收益日历相关
                    profitCalendarVisible,
                    selectedProfitAccountType,
                    profitCalendarData,
                    calendarMonth,
                    calendarDays,
                    profitDisplayMode,
                    viewProfitCalendar,
                    handleCloseProfitCalendar,
                    handleMonthChange,
                    handleDayClick,
                    formatCalendarTitle,
                    formatProfitRate,
                    // 工具函数
                    getTradingTimeStatus,
                    formatNumber,
                    validateStockSymbol,
                    showConfirmDialog,
                    // 统计函数
                    getTradingDaysCount,
                    getProfitDaysCount,
                    getAverageDailyProfit,
                    getWinRate,
                    getMaxDailyProfit,
                    getMaxDailyLoss,
                }
            }
        })

        app.use(ElementPlus)
        app.config.globalProperties.$message = ElMessage
        app.config.globalProperties.$messageBox = ElMessageBox
        app.mount('#app')
    </script>
</body>

</html> 
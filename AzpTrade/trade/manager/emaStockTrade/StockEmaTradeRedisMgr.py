"""
股票交易管理模块的Redis操作
实现股票交易相关的Redis存取操作，包括：
1. T+0交易状态管理
2. 均线持仓池状态管理
"""

import sys
import os
import time
import json
import threading
import datetime
from functools import wraps

import akshare as ak
import numpy as np
import pandas as pd

import trade.log.YfyLog as yfylog
import trade.manager.TimeDateMgr as timeDateMgr
import trade.network.YFYAccountMgr as yfyam
import trade.redis.StockRedisMgr as redisMgr
from trade.redis.StockRedisMgr import RedisKeyPrefix
from enum import Enum

# 在RedisKeyPrefix中添加均线持仓池相关的键前缀
# 这需要在trade.redis.StockRedisMgr中实现，但这里我们暂时使用字符串常量

# 均线持仓池状态的Redis键前缀
MA_POSITION_STATUS_PREFIX = RedisKeyPrefix.TRADE_STOCK.value + ':ema:status'
# 均线持仓池最后交易时间的Redis键前缀
MA_LAST_TRADE_PREFIX = RedisKeyPrefix.TRADE_STOCK.value + ':ema:last_trade'
# 均线持仓池交易记录的Redis键前缀
MA_TRADE_RECORD_PREFIX = RedisKeyPrefix.TRADE_STOCK.value + ':ema:record'
# T+0交易阈值执行记录的Redis键前缀
T0_THRESHOLD_EXEC_PREFIX = RedisKeyPrefix.TRADE_STOCK.value + ':t0:threshold_exec'
# T+0尾盘买回执行记录的Redis键前缀
T0_BUYBACK_EXEC_PREFIX = RedisKeyPrefix.TRADE_STOCK.value + ':t0:buyback_exec'
# 均线跌破卖出记录的Redis键前缀
MA_BREAKDOWN_SELL_PREFIX = RedisKeyPrefix.TRADE_STOCK.value + ':ema:breakdown_sell'

class T0TradeStatus(Enum):
    """T+0交易状态枚举"""
    NO_TRADE = "no_trade"           # 未交易
    SOLD_FIRST_LEVEL = "sold_first"  # 已卖出第一档
    SOLD_SECOND_LEVEL = "sold_second" # 已卖出第二档
    BOUGHT_BACK = "bought_back"      # 已买回


def trading_time_required(func):
    """
    装饰器：检查当前是否为交易时间，非交易时间不执行Redis写入操作
    
    仅对写入操作函数进行时间检查，读取操作不受影响
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 检查当前是否为交易时间
        is_work_day, is_opening, is_bidding = timeDateMgr.is_open_time()
        
        # 非交易时间跳过写入操作
        if not (is_work_day and (is_opening or is_bidding)):
            yfylog.logger.info(f"当前非交易时间，跳过Redis写入操作: {func.__name__}")
            return False  # 返回False表示操作被跳过
        
        # 交易时间内正常执行函数
        return func(*args, **kwargs)
    
    return wrapper


#---------------------- T+0交易相关操作 ----------------------#

def get_t0_trade_status(symbol, account_type):
    """获取股票的T+0交易状态
    Args:
        symbol: 股票代码
        account_type: 账户类型
    Returns:
        T0TradeStatus: 交易状态枚举值
    """
    redis_mgr = redisMgr.StockRedisMgr()
    today_date = datetime.datetime.now().strftime("%Y%m%d")
    key = redis_mgr.build_key(RedisKeyPrefix.TRADE_T0_STATUS.value, symbol, account_type, today_date)
    status = redis_mgr.get_value(key)
    
    if not status:
        return T0TradeStatus.NO_TRADE
    
    try:
        return T0TradeStatus(status)
    except ValueError:
        yfylog.logger.error(f"无效的T+0交易状态: {status}")
        return T0TradeStatus.NO_TRADE

@trading_time_required
def update_t0_trade_status(symbol, account_type, status):
    """更新股票的T+0交易状态
    Args:
        symbol: 股票代码
        account_type: 账户类型
        status: T0TradeStatus枚举值
    """
    redis_mgr = redisMgr.StockRedisMgr()
    today_date = datetime.datetime.now().strftime("%Y%m%d")
    key = redis_mgr.build_key(RedisKeyPrefix.TRADE_T0_STATUS.value, symbol, account_type, today_date)
    redis_mgr.set_value(key, status.value, expire=24*60*60)  # 24小时过期
    yfylog.logger.info(f"更新T+0交易状态: {symbol} {account_type} {status.value} 日期: {today_date}")

@trading_time_required
def record_t0_trade_position(symbol, account_type, sold_pct, sold_amount, reason=""):
    """记录T+0交易卖出的持仓信息，用于尾盘买回
    Args:
        symbol: 股票代码
        account_type: 账户类型
        sold_pct: 卖出比例
        sold_amount: 卖出金额
        reason: 交易原因
    """
    redis_mgr = redisMgr.StockRedisMgr()
    today_date = datetime.datetime.now().strftime("%Y%m%d")
    key = redis_mgr.build_key(RedisKeyPrefix.TRADE_T0_POSITION.value, symbol, account_type, today_date)
    
    # 获取现有记录
    position_data = redis_mgr.get_value(key) or {"total_sold_pct": 0, "total_sold_amount": 0, "trades": [], "date": today_date}
    
    # 更新数据
    position_data["total_sold_pct"] += sold_pct
    position_data["total_sold_amount"] += sold_amount
    position_data["trades"].append({
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "sold_pct": sold_pct,
        "sold_amount": sold_amount,
        "reason": reason
    })
    
    # 保存到Redis
    redis_mgr.set_value(key, position_data, expire=24*60*60)  # 24小时过期
    yfylog.logger.info(f"记录T+0交易持仓: {symbol} {account_type} 卖出比例: {sold_pct:.2f} 卖出金额: {sold_amount} 日期: {today_date}")
    
    # 同时记录交易历史
    record_key = redis_mgr.build_key(RedisKeyPrefix.TRADE_T0_RECORD.value, symbol, account_type, today_date, int(time.time()))
    trade_record = {
        "symbol": symbol,
        "account_type": account_type,
        "trade_type": "sell",
        "percentage": sold_pct,
        "amount": sold_amount,
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "reason": reason,
        "date": today_date
    }
    redis_mgr.set_value(record_key, trade_record, expire=7*24*60*60)  # 保存一周

def get_t0_trade_position(symbol, account_type):
    """获取T+0交易卖出的持仓信息
    Args:
        symbol: 股票代码
        account_type: 账户类型
    Returns:
        dict: 持仓信息
    """
    redis_mgr = redisMgr.StockRedisMgr()
    today_date = datetime.datetime.now().strftime("%Y%m%d")
    key = redis_mgr.build_key(RedisKeyPrefix.TRADE_T0_POSITION.value, symbol, account_type, today_date)
    position_data = redis_mgr.get_value(key) or {"total_sold_pct": 0, "total_sold_amount": 0, "trades": [], "date": today_date}
    return position_data

@trading_time_required
def record_t0_buy_back(symbol, account_type, total_sold_amount, msg):
    """记录T+0买回交易
    Args:
        symbol: 股票代码
        account_type: 账户类型
        total_sold_amount: 买回总金额
        msg: 买回原因
    """
    try:
        # 获取当前日期
        today_date = datetime.datetime.now().strftime("%Y%m%d")
        record_key = redisMgr.StockRedisMgr().build_key(RedisKeyPrefix.TRADE_T0_RECORD.value, symbol, account_type, today_date, int(time.time()))
        trade_record = {
            "symbol": symbol,
            "account_type": account_type,
            "trade_type": "buyback",
            "amount": total_sold_amount,
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "reason": msg,
            "date": today_date
        }
        redisMgr.StockRedisMgr().set_value(record_key, trade_record, expire=7*24*60*60)  # 保存一周
        yfylog.logger.info(f"记录股票 {symbol} {account_type} T+0买回信息成功，买回金额: {total_sold_amount}")
    except Exception as e:
        yfylog.logger.error(f"记录股票 {symbol} {account_type} T+0买回信息失败: {str(e)}")


#---------------------- 均线持仓池相关操作 (新架构简化版) ----------------------#

def get_ma_position_status(symbol, account_type, ma_name):
    """获取均线池的持仓状态 (重构版)
    
    在新架构下，只关心 position 和用于止损的 buy_price/buy_time
    
    Args:
        symbol: 股票代码
        account_type: 账户类型
        ma_name: 均线名称
    Returns:
        dict: 持仓状态信息
    """
    redis_mgr = redisMgr.StockRedisMgr()
    key = redis_mgr.build_key(MA_POSITION_STATUS_PREFIX, symbol, account_type, ma_name)
    status = redis_mgr.get_value(key)
    
    if not status or not isinstance(status, dict):
        # 如果没有记录或格式不正确，返回清晰的默认值
        return {
            "position": False,
            "buy_price": 0.0,
            "buy_time": "",
        }
    
    return status

@trading_time_required
def update_ma_position_status(symbol, account_type, ma_name, position_data):
    """更新均线池的持仓状态 (重构版)
    
    直接将传入的字典存入Redis，不做额外处理。
    调用方应确保 position_data 包含所需字段 (如 'position', 'last_update')。
    
    Args:
        symbol: 股票代码
        account_type: 账户类型
        ma_name: 均线名称
        position_data: 要存储的状态字典
    """
    redis_mgr = redisMgr.StockRedisMgr()
    key = redis_mgr.build_key(MA_POSITION_STATUS_PREFIX, symbol, account_type, ma_name)
    redis_mgr.set_value(key, position_data, expire=60*24*60*60) # 保存60天

def get_all_ma_position_status(symbol, account_type):
    """获取股票所有均线池的持仓状态（优化版本）
    Args:
        symbol: 股票代码
        account_type: 账户类型
    Returns:
        dict: 以均线名称为键，持仓状态为值的字典
    """
    redis_mgr = redisMgr.StockRedisMgr()
    pattern = redis_mgr.build_key(MA_POSITION_STATUS_PREFIX, symbol, account_type, "*")
    keys = redis_mgr.keys(pattern)

    ma_positions = {}
    if not keys:
        return ma_positions

    # 使用批量获取优化性能
    try:
        batch_data = redis_mgr.batch_get(keys)
        for key, status_data in batch_data.items():
            key_parts = key.split(":")
            if len(key_parts) >= 5:
                ma_name = key_parts[-1]
                ma_positions[ma_name] = status_data
    except Exception as e:
        yfylog.logger.error(f"批量获取股票均线池状态失败: {e}")
        # 降级到单个获取
        for key in keys:
            key_parts = key.split(":")
            if len(key_parts) >= 5:
                ma_name = key_parts[-1]
                ma_positions[ma_name] = redis_mgr.get_value(key)

    return ma_positions

def update_all_ma_positions_status(symbol, account_type, all_ma_pools, active_ma_pools):
    """
    一次性更新指定股票和账户下所有均线池的状态 (新架构 v2)。
    
    - 遍历所有已知的均线池。
    - 如果一个池在 active_ma_pools 集合中，则其状态的 position 设为 True。
    - 否则，设为 False。
    - 仅在状态发生变化时才写入Redis，以减少不必要的操作。
    """
    yfylog.logger.info(f"开始批量更新Redis状态 for {symbol} {account_type}, 激活池: {active_ma_pools}")
    for ma_name in all_ma_pools:
        should_be_active = ma_name in active_ma_pools
        
        current_status = get_ma_position_status(symbol, account_type, ma_name)
        
        # 只有当期望状态与当前状态不同时才更新
        if current_status.get("position") != should_be_active:
            yfylog.logger.info(f"同步状态: {symbol} {account_type} {ma_name} 状态从 {current_status.get('position')} -> {should_be_active}")
            
            new_status = {
                "position": should_be_active,
                "buy_price": current_status.get("buy_price", 0.0), # 保留旧数据
                "buy_time": current_status.get("buy_time", ""),     # 保留旧数据
                "last_update": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            update_ma_position_status(symbol, account_type, ma_name, new_status)
    yfylog.logger.info(f"批量更新Redis状态完成 for {symbol} {account_type}")


#---------------------- T+0交易(保留部分) ----------------------#

def has_executed_buyback_today(symbol, account_type, today_date=None):
    """检查今日是否已执行过尾盘买回
    
    Args:
        symbol: 股票代码
        account_type: 账户类型
        today_date: 日期字符串，默认为当天
        
    Returns:
        bool: 是否已执行过买回
    """
    try:
        if today_date is None:
            today_date = datetime.datetime.now().strftime("%Y%m%d")
            
        key = f"{RedisKeyPrefix.TRADE_STOCK.value}:{T0_BUYBACK_EXEC_PREFIX}:{today_date}:{symbol}:{account_type}"
        
        # 检查Redis中是否存在该键
        redis_mgr = redisMgr.StockRedisMgr()
        return bool(redis_mgr.exists(key))
    except Exception as e:
        yfylog.logger.error(f"检查股票 {symbol} {account_type} 今日买回状态失败: {str(e)}")
        return False


@trading_time_required
def mark_buyback_executed(symbol, account_type, buyback_amount, today_date=None):
    """标记已执行过尾盘买回
    
    Args:
        symbol: 股票代码
        account_type: 账户类型
        buyback_amount: 买回金额
        today_date: 日期字符串，默认为当天
    """
    try:
        if today_date is None:
            today_date = datetime.datetime.now().strftime("%Y%m%d")
            
        key = f"{RedisKeyPrefix.TRADE_STOCK.value}:{T0_BUYBACK_EXEC_PREFIX}:{today_date}:{symbol}:{account_type}"
        
        # 记录执行时间和金额
        buyback_data = {
            "executed_time": datetime.datetime.now().strftime("%H:%M:%S"),
            "buyback_amount": buyback_amount
        }
        
        # 写入Redis并设置当天有效期
        redis_mgr = redisMgr.StockRedisMgr()
        redis_mgr.set_value(key, buyback_data)
        
        # 设置过期时间为当天结束(23:59:59)
        today_end = datetime.datetime.combine(
            datetime.datetime.now().date(), 
            datetime.time(23, 59, 59)
        )
        seconds_until_end = int((today_end - datetime.datetime.now()).total_seconds())
        redis_mgr.expire(key, max(seconds_until_end, 14400))  # 至少保存1小时
        
        yfylog.logger.info(f"标记股票 {symbol} {account_type} 今日已执行尾盘买回")
    except Exception as e:
        yfylog.logger.error(f"标记股票 {symbol} {account_type} 买回状态时发生错误: {str(e)}")


def has_executed_threshold_level(symbol, account_type, threshold_idx, today_date=None):
    """检查某个阈值级别是否已经执行过交易
    Args:
        symbol: 股票代码
        account_type: 账户类型
        threshold_idx: 阈值级别索引（0表示第一档，1表示第二档）
        today_date: 日期字符串，格式为"YYYYMMDD"，默认为当天
    Returns:
        bool: 是否已执行过该阈值级别的交易
    """
    if today_date is None:
        today_date = datetime.datetime.now().strftime("%Y%m%d")
        
    key = f"{RedisKeyPrefix.TRADE_STOCK.value}:{T0_THRESHOLD_EXEC_PREFIX}:{today_date}:{symbol}:{account_type}:{threshold_idx}"
    redis_mgr = redisMgr.StockRedisMgr()
    return bool(redis_mgr.exists(key))


@trading_time_required
def mark_threshold_level_executed(symbol, account_type, threshold_idx, change_pct, sell_pct, sell_amount, today_date=None):
    """标记某个阈值级别已执行交易
    Args:
        symbol: 股票代码
        account_type: 账户类型
        threshold_idx: 阈值级别索引
        change_pct: 执行时的涨跌幅
        sell_pct: 卖出比例
        sell_amount: 卖出金额
        today_date: 日期字符串，格式为"YYYYMMDD"，默认为当天
    """
    if today_date is None:
        today_date = datetime.datetime.now().strftime("%Y%m%d")
        
    key = f"{RedisKeyPrefix.TRADE_STOCK.value}:{T0_THRESHOLD_EXEC_PREFIX}:{today_date}:{symbol}:{account_type}:{threshold_idx}"
    
    # 准备执行记录数据
    exec_data = {
        "executed_time": datetime.datetime.now().strftime("%H:%M:%S"),
        "change_pct": change_pct,
        "sell_pct": sell_pct,
        "sell_amount": sell_amount
    }
    
    # 保存到Redis
    redis_mgr = redisMgr.StockRedisMgr()
    redis_mgr.set_value(key, exec_data)
    yfylog.logger.info(f"标记T+0阈值级别已执行: {symbol} {account_type} 第{threshold_idx+1}档 涨跌幅: {change_pct:.2f}% 卖出比例: {sell_pct:.2f} 卖出金额: {sell_amount:.2f}")


def reset_daily_t0_trading_state():
    """重置每日T+0交易状态（优化版本）
    在每个交易日开始时调用此函数，清除前一天的交易记录和状态
    优化：使用批量删除减少Redis操作次数
    """
    try:
        # 获取昨天的日期
        yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y%m%d")
        yfylog.logger.info(f"开始重置股票每日T+0交易状态，清除日期: {yesterday}")

        redis_mgr = redisMgr.StockRedisMgr()

        # 定义所有需要清除的键模式
        key_patterns = [
            f"{RedisKeyPrefix.TRADE_STOCK.value}:t0:status:{yesterday}:*",
            f"{RedisKeyPrefix.TRADE_STOCK.value}:t0:position:{yesterday}:*",
            f"{RedisKeyPrefix.TRADE_STOCK.value}:{T0_THRESHOLD_EXEC_PREFIX}:{yesterday}:*",
            f"{RedisKeyPrefix.TRADE_STOCK.value}:{T0_BUYBACK_EXEC_PREFIX}:{yesterday}:*"
        ]

        pattern_names = ["交易状态", "交易记录", "阈值执行记录", "买回执行记录"]

        # 批量收集所有需要删除的键
        all_keys_to_delete = []
        for i, pattern in enumerate(key_patterns):
            keys = redis_mgr.keys(pattern)
            if keys:
                yfylog.logger.info(f"找到昨日股票T+0{pattern_names[i]}: {len(keys)}条")
                all_keys_to_delete.extend(keys)

        # 批量删除所有键（如果有的话）
        if all_keys_to_delete:
            deleted_count = redis_mgr.delete_keys(all_keys_to_delete)
            yfylog.logger.info(f"批量删除昨日股票T+0数据: 成功删除 {deleted_count}/{len(all_keys_to_delete)} 条记录")
        else:
            yfylog.logger.info("没有找到需要清除的昨日股票T+0数据")

        yfylog.logger.info("股票每日T+0交易状态重置完成")
    except Exception as e:
        yfylog.logger.error(f"重置股票每日T+0交易状态失败: {str(e)}")

@trading_time_required
def record_ma_breakdown_sell(symbol, account_type, ma_name, sell_price, sell_reason):
    """记录因均线跌破而卖出的信息
    
    用于防止当天跌破均线后的重复买入，必须重新站上均线才能买入
    
    Args:
        symbol: 股票代码
        account_type: 账户类型
        ma_name: 均线名称 (如 'ma5', 'ma10')
        sell_price: 卖出价格
        sell_reason: 卖出原因
    """
    try:
        today_date = datetime.datetime.now().strftime("%Y%m%d")
        key = f"{MA_BREAKDOWN_SELL_PREFIX}:{today_date}:{symbol}:{account_type}:{ma_name}"
        
        breakdown_data = {
            "sell_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "sell_price": sell_price,
            "sell_reason": sell_reason,
            "ma_name": ma_name,
            "symbol": symbol,
            "account_type": account_type,
            "date": today_date
        }
        
        redis_mgr = redisMgr.StockRedisMgr()
        redis_mgr.set_value(key, breakdown_data, expire=24*60*60)  # 24小时过期
        yfylog.logger.info(f"记录均线跌破卖出: {symbol} {account_type} {ma_name} 卖出价格: {sell_price:.2f} 原因: {sell_reason}")
        
    except Exception as e:
        yfylog.logger.error(f"记录均线跌破卖出信息失败: {symbol} {account_type} {ma_name} - {str(e)}")

def has_ma_breakdown_sell_today(symbol, account_type, ma_name, today_date=None):
    """检查当天是否因该均线跌破而卖出过
    
    Args:
        symbol: 股票代码
        account_type: 账户类型
        ma_name: 均线名称
        today_date: 日期字符串，默认为当天
        
    Returns:
        tuple: (是否有跌破卖出记录, 卖出数据)
    """
    try:
        if today_date is None:
            today_date = datetime.datetime.now().strftime("%Y%m%d")
            
        key = f"{MA_BREAKDOWN_SELL_PREFIX}:{today_date}:{symbol}:{account_type}:{ma_name}"
        
        redis_mgr = redisMgr.StockRedisMgr()
        breakdown_data = redis_mgr.get_value(key)
        
        if breakdown_data:
            yfylog.logger.info(f"发现当天均线跌破卖出记录: {symbol} {account_type} {ma_name} 时间: {breakdown_data.get('sell_time')}")
            return True, breakdown_data
        else:
            return False, None
            
    except Exception as e:
        yfylog.logger.error(f"检查均线跌破卖出记录失败: {symbol} {account_type} {ma_name} - {str(e)}")
        return False, None

@trading_time_required
def clear_ma_breakdown_sell_record(symbol, account_type, ma_name, today_date=None):
    """清除均线跌破卖出记录（当重新站上均线并买入后调用）
    
    Args:
        symbol: 股票代码
        account_type: 账户类型
        ma_name: 均线名称
        today_date: 日期字符串，默认为当天
    """
    try:
        if today_date is None:
            today_date = datetime.datetime.now().strftime("%Y%m%d")
            
        key = f"{MA_BREAKDOWN_SELL_PREFIX}:{today_date}:{symbol}:{account_type}:{ma_name}"
        
        redis_mgr = redisMgr.StockRedisMgr()
        redis_mgr.delete_keys(key)
        yfylog.logger.info(f"清除均线跌破卖出记录: {symbol} {account_type} {ma_name}")
        
    except Exception as e:
        yfylog.logger.error(f"清除均线跌破卖出记录失败: {symbol} {account_type} {ma_name} - {str(e)}")

def get_all_ma_breakdown_sells_today(symbol, account_type, today_date=None):
    """获取当天所有均线跌破卖出记录
    
    Args:
        symbol: 股票代码
        account_type: 账户类型
        today_date: 日期字符串，默认为当天
        
    Returns:
        dict: 以均线名称为键的跌破卖出记录字典
    """
    try:
        if today_date is None:
            today_date = datetime.datetime.now().strftime("%Y%m%d")
            
        pattern = f"{MA_BREAKDOWN_SELL_PREFIX}:{today_date}:{symbol}:{account_type}:*"
        redis_mgr = redisMgr.StockRedisMgr()
        keys = redis_mgr.keys(pattern)
        
        breakdown_records = {}
        for key in keys:
            key_parts = key.split(":")
            if len(key_parts) >= 7:
                ma_name = key_parts[-1]
                breakdown_data = redis_mgr.get_value(key)
                if breakdown_data:
                    breakdown_records[ma_name] = breakdown_data
        
        return breakdown_records
        
    except Exception as e:
        yfylog.logger.error(f"获取所有均线跌破卖出记录失败: {symbol} {account_type} - {str(e)}")
        return {}

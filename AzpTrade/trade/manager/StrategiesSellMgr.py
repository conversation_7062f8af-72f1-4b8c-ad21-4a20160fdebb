import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

import pandas as pd

import trade.network.YFYDataMgr as yfydm
import trade.log.YfyLog as yfyLog
import trade.manager.TimeDateMgr as tdm
import trade.network.YFYAccountMgr as yfyam
from trade.enum.WxMsgType import WxMsgType
import datetime
import trade.strategy.ZhiBiaoUtils as zbUtils
import trade.strategy.StrategiesUtils as sUtils
import trade.strategy.StrategiesConfig as sconfig
import trade.manager.ThirdDataMgr as thirdDataMgr
import trade.enum.StategiesSellType as sType
import trade.zhibiao.MyTT as mt
import trade.manager.StockKinfoAsyncMgr as stockMgr
import numpy as np
import trade.manager.FundsPosMgr as fpm
from trade.network.yfydata_mPriceInfo import YFYDataMinutePriceInfo
from trade.redis.StockRedisMgr import StockRedisMgr, RedisKeyPrefix


def filter_significant_values(values, min_gap_percent=2, count=3):
    """
    从价格列表中筛选出差距显著的值
    :param values: 价格列表
    :param min_gap_percent: 最小差距百分比，相邻值之间至少要有这个百分比的差距（例如：2表示2%）
    :param count: 需要返回的值的个数
    :return: count个差距显著的值的列表，从大到小排序
    """
    # 参数验证
    if not values or not isinstance(count, int) or count <= 0:
        return []
    if min_gap_percent <= 0 or min_gap_percent >= 100:
        min_gap_percent = 2  # 使用默认值

    # 类型检查
    if not all(isinstance(x, (int, float)) for x in values if x is not None):
        return []

    # 移除None值和0值，并排序（从大到小）
    values = sorted([x for x in values if x is not None and x > 0], reverse=True)
    if not values:
        return []

    # 如果只需要一个值，直接返回最大值
    if count == 1:
        return [round(values[0], 2)]

    min_gap = min_gap_percent / 100.0
    max_iterations = len(values) * 3  # 防止死循环

    # 第一步：生成所有满足最小差距的值
    result = [values[0]]  # 从最大值开始
    i = 1
    iteration_count = 0

    # 先尝试使用原始值
    while i < len(values) and iteration_count < max_iterations:
        current = result[-1]
        next_val = values[i]

        # 如果差距不够，在中间插入新值
        if current / next_val - 1 < min_gap:
            new_val = round(current / (1 + min_gap), 2)
            # 如果新生成的值已经小于下一个原始值，就跳过这个原始值
            if new_val <= next_val:
                i += 1
                iteration_count += 1
                continue
            result.append(new_val)
        else:
            result.append(next_val)
            i += 1
            iteration_count += 1

    # 确保最后一个值不小于原始最小值
    while len(result) > 1 and result[-1] < values[-1]:
        result.pop()

    # 如果结果数量不够，继续生成新值
    iteration_count = 0
    while len(result) < count and iteration_count < max_iterations:
        # 找到最大间隔
        max_gap = 0
        insert_index = 1

        if len(result) < 2:
            # 只有一个值时，直接在后面添加新值
            new_val = round(result[-1] / (1 + min_gap), 2)
            if new_val >= values[-1]:
                result.append(new_val)
            else:
                break  # 如果无法生成有效值，就退出
            continue

        # 找到最大间隔
        for j in range(1, len(result)):
            gap = result[j - 1] / result[j] - 1
            if gap > max_gap:
                max_gap = gap
                insert_index = j

        # 在最大间隔处插入新值
        if max_gap > min_gap:
            new_val = round(result[insert_index - 1] / (1 + min_gap), 2)
            if new_val > result[insert_index]:
                result.insert(insert_index, new_val)
            else:
                # 如果无法在间隔中插入新值，尝试在末尾添加
                new_val = round(result[-1] / (1 + min_gap), 2)
                if new_val >= values[-1]:
                    result.append(new_val)
                else:
                    break
        else:
            # 如果没有足够大的间隔，在最后添加值
            new_val = round(result[-1] / (1 + min_gap), 2)
            if new_val >= values[-1]:
                result.append(new_val)
            else:
                break  # 如果新值会小于最小值，则停止生成

        iteration_count += 1

    # 如果结果数量大于需要的数量，选择分布最均匀的值
    if len(result) > count:
        # 计算理想的间隔
        total_range = result[0] - result[-1]
        ideal_gap = total_range / (count - 1)

        # 选择最接近理想间隔的值
        final_result = [result[0]]  # 总是包含最大值
        current_value = result[0]

        for _ in range(count - 2):
            target = current_value - ideal_gap
            # 找最接近target的值，但必须小于current_value
            candidates = [x for x in result if x < current_value]
            if not candidates:
                break
            closest = min(candidates, key=lambda x: abs(x - target))
            final_result.append(closest)
            current_value = closest

        # 确保结果包含最小值
        if len(final_result) < count:
            final_result.append(result[-1])

        final_result = [round(x, 2) for x in final_result]
        return final_result

    result = [round(x, 2) for x in result]
    return result


sell_symbol_data_dict = {}

'''
    卖出策略逻辑：
    1.高标票，如果3日涨跌幅或者5日涨跌幅比较大，则以5、10日均线做为卖出点，30分钟ATR做为最后止盈止损点。
    2.如果是长【上升】趋势票，则以5、10均线做为卖出点，30分钟ATR做为最后止盈止损点。
    3.如果是长【下跌】趋势票，则以反转的均线做为不同的卖出点，挂一个最大止损卖
    4.如果是上面3者之外的，则以5、10均线做为卖出点，30分钟ATR做为最后止盈止损点。
'''


def sell_stock(type: str = sType.SELL_MIN_SINGAL, period: str = "30", sell_scale: float = 1, buy_strategy: str = ""):
    if type == sType.SELL_MIN_SINGAL:
        sell_stock_minSingal(period, sell_scale)
    elif type == sType.SELL_DAY_SINGAL:
        sell_stock_daySingal(sell_scale)
    elif type == sType.SELL_KTN_SINGAL or type == sType.SELL_TAQ_SINGAL or type == sType.SELL_ATR_SINGAL:
        sell_stock_atr(type, period, sell_scale)
    elif type == sType.SELL_DTEMA:
        sell_stock_dtema(period, sell_scale)
    elif sType.SELL_STRATEGY:
        sell_stock_strategy(buy_strategy, sell_scale)


def sell_stock_minSingal(period: str = "60", sell_scale: float = 1, set_sell_zb_list=None, sell_symbol_list=None):
    global sell_symbol_data_dict
    isword_day, is_opening, is_Bidding = tdm.is_open_time()
    if not is_opening or tdm.debug_str == "debug":
        yfydm.push_wx_msg(WxMsgType.STRATEGY_SELL, "不在交易时间/debug,暂停交易,卖出失效")
        return
    if set_sell_zb_list is None:
        set_sell_zb_list = []

    if sell_symbol_list is not None and len(sell_symbol_list) > 0:
        stock_list = sell_symbol_list  # 如果给定了特定的指定卖出的票
    else:
        stock_list = yfyam.get_all_account_pos_symbols()
    account_type_list = yfyam.account_types.keys()
    hour_time = datetime.datetime.now().strftime("%Y%m%d%H%M")
    if period == "5":
        set_sell_zb_list = ["longSell", "jiancangSell", "taoDingSell", "QSDBLSell", "gdSell"]

    sell_zb_list = zbUtils.calc_zb(symbol_list=stock_list, win_count=1, need_zb_cnt=1, period=period,
                                   zb_type="sell", is_live=True, set_sell_list=set_sell_zb_list)  # 计算买信号
    for symbol in sell_zb_list:
        hour_time_key = symbol + "_" + sType.SELL_MIN_SINGAL + "_" + hour_time
        msg = symbol + "-" + f"[{period}]分钟卖信号卖-" + hour_time
        is_up_limit = False  # dataMgr.is_daily_limit(symbol)
        if is_up_limit:
            yfyLog.logger.info("++【%s】涨停票，不执行卖出逻辑！！！", symbol)
            continue
        if hour_time_key not in sell_symbol_data_dict:
            for account_type in account_type_list:
                stock_info = yfyam.get_account_stock_info(account_type=account_type, symbol=symbol)
                if "availableAmount" in stock_info and stock_info["availableAmount"] > 0:
                    yfyLog.logger.info("[%s]--%s分钟卖信号卖信号卖出股票,%s", account_type, period, msg)
                    yfyam.sell_stock(account_type=account_type, symbol=symbol, sell_pct=0.03 * sell_scale, msg=msg)
                    sell_symbol_data_dict[hour_time_key] = msg


def sell_stock_daySingal(sell_scale: float = 1):
    global sell_symbol_data_dict
    isword_day, is_opening, is_Bidding = tdm.is_open_time()
    if not is_opening or tdm.debug_str == "debug":
        yfydm.push_wx_msg(WxMsgType.STRATEGY_SELL, "不在交易时间/debug,暂停交易,卖出失效")
        return

    stock_list = yfyam.get_all_account_pos_symbols()
    account_type_list = yfyam.account_types.keys()
    hour_time = datetime.datetime.now().strftime("%Y%m%d")

    for symbol in stock_list:
        buy_signal_dict, sell_signal_dict = yfydm.get_stock_signal_dicts(symbol=symbol, day_count=0)
        hour_time_key = symbol + "_" + sType.SELL_DAY_SINGAL + "_" + hour_time
        sell_cnt = len(sell_signal_dict)
        if sell_cnt > 0:
            msg = symbol + "-" + "日K出现卖信号卖-" + hour_time
            is_up_limit = False  # dataMgr.is_daily_limit(symbol)
            if is_up_limit:
                yfyLog.logger.info("++【%s】涨停票，不执行卖出逻辑！！！", symbol)
                continue
            if hour_time_key not in sell_symbol_data_dict:
                for account_type in account_type_list:
                    stock_info = yfyam.get_account_stock_info(account_type=account_type, symbol=symbol)
                    if "availableAmount" in stock_info and stock_info["availableAmount"] > 0:
                        yfyLog.logger.info("[%s]--日K出现卖信号卖出股票,%s", account_type, msg)
                        yfyam.sell_stock(account_type=account_type, symbol=symbol,
                                         sell_pct=0.05 * sell_cnt * sell_scale,
                                         msg=msg)
                        sell_symbol_data_dict[hour_time_key] = msg


def sell_stock_atr(type: str = sType.SELL_KTN_SINGAL, period: str = "30", sell_scale: float = 1):
    global sell_symbol_data_dict
    isword_day, is_opening, is_Bidding = tdm.is_open_time()
    if not is_opening or tdm.debug_str == "debug":
        yfydm.push_wx_msg(WxMsgType.STRATEGY_SELL, "不在交易时间/debug,暂停交易,卖出失效")
        return

    stock_list = yfyam.get_all_account_pos_symbols()
    account_type_list = yfyam.account_types.keys()
    hour_time = datetime.datetime.now().strftime("%Y%m%d")

    if type == sType.SELL_KTN_SINGAL or type == sType.SELL_TAQ_SINGAL or type == sType.SELL_ATR_SINGAL:
        zbUtils.calc_up_mid_down(symbol_list=stock_list, period=period, type=type)
        for symbol in stock_list:
            key = zbUtils.get_k_time_key(symbol=symbol, period=period, type=type)
            knt_list = zbUtils.up_mid_down_dict[key]
            upper, mid, lower = knt_list[0], knt_list[1], knt_list[2]
            stock_simple_info = yfydm.get_stock_simple_info(symbol)
            close = stock_simple_info["close"].values[0]
            # print(symbol, close, lower[-1], mid[-1], (close - lower[-1]) / lower[-1] * 100,
            #      (close - mid[-1]) / mid[-1] * 100)
            msg = symbol + "-" + "ATR通道止损出现卖信号卖-" + "close:-" + str(close) + "lower:-" + str(
                lower[-1]) + "-mid:-" + f"{mid[-1]:.2f}"
            hour_time_key = symbol + "_" + type + "_" + hour_time
            # print(msg)
            if close < mid[-1] and hour_time_key not in sell_symbol_data_dict:
                for account_type in account_type_list:
                    stock_info = yfyam.get_account_stock_info(account_type=account_type, symbol=symbol)
                    if "availableAmount" in stock_info and stock_info["availableAmount"] > 0:
                        yfyLog.logger.info("[%s]--[%s]K出现卖信号卖出股票,%s,当前价：%s,止损价：%s", account_type, period,
                                           symbol, close, lower[-1])
                        sell_pct = 1 if close < lower[-1] else 0.2
                        sell_pct = sell_pct * sell_scale
                        yfyam.sell_stock(account_type=account_type, symbol=symbol, sell_pct=sell_pct,
                                         msg=msg)
                        sell_symbol_data_dict[hour_time_key] = msg


def sell_stock_dtema(period: str = "30", sell_scale: float = 1):
    global sell_symbol_data_dict
    isword_day, is_opening, is_Bidding = tdm.is_open_time()
    if not is_opening or tdm.debug_str == "debug":
        yfydm.push_wx_msg(WxMsgType.STRATEGY_SELL, "不在交易时间/debug,暂停交易,卖出失效")
        return

    stock_list = yfyam.get_all_account_pos_symbols()
    account_type_list = yfyam.account_types.keys()
    hour_time = datetime.datetime.now().strftime("%Y%m%d")

    zbUtils.calc_demat(symbol_list=stock_list, period=period)
    for symbol in stock_list:
        key = zbUtils.get_k_time_key(symbol=symbol, period=period, type="demat")
        demat_list = zbUtils.demat_dict[key]
        temab, demab, tmab = demat_list[0], demat_list[1], demat_list[2]
        stock_simple_info = yfydm.get_stock_simple_info(symbol)
        close = stock_simple_info["close"].values[0]
        # print(symbol, close, lower[-1], mid[-1], (close - lower[-1]) / lower[-1] * 100,
        #      (close - mid[-1]) / mid[-1] * 100)
        msg = symbol + "-" + "DEMAT分别为:" + "close:-" + str(
            close) + "【temab:-" + f"{temab[-1]:.2f}" + "-demab:-" + f"{demab[-1]:.2f}" + "-tmab:-" + f"{tmab[-1]:.2f}】"
        hour_time_key = symbol + "_" + type + "_" + hour_time
        # print(msg)
        if close < temab[-1] and hour_time_key not in sell_symbol_data_dict:
            for account_type in account_type_list:
                stock_info = yfyam.get_account_stock_info(account_type=account_type, symbol=symbol)
                if "availableAmount" in stock_info and stock_info["availableAmount"] > 0:
                    yfyLog.logger.info("[%s]--[%s]K出现卖信号卖出股票,%s,当前价：%s,temba：%s,demba：%s,tmba：%s,",
                                       account_type, period, symbol, close, temab[-1], demab[-1], tmab[-1])
                    sell_pct = 0.2
                    if close < demab[-1]:
                        sell_pct += 0.4
                    if close < tmab[-1]:
                        sell_pct += 1
                    sell_pct = min(1, sell_pct) * sell_scale
                    yfyam.sell_stock(account_type=account_type, symbol=symbol, sell_pct=sell_pct, msg=msg)
                    sell_symbol_data_dict[hour_time_key] = msg


def get_ema_sell_str(close, ema_dict):
    # 先按值降序排列
    sorted_ema = sorted(ema_dict.items(), key=lambda item: item[1], reverse=True)
    sorted_ema_dict = {}
    for key, value in sorted_ema:
        if close >= value:
            sorted_ema_dict[key] = value

    sell_pct_count = 100
    sell_str = "#均线卖:{"
    for key, value in sorted_ema_dict.items():
        # sell_strategy_str = "#均线卖:{30%:MA3+50%:MA5+10%:MA10}"
        sell_pct = round(sell_pct_count * 0.4)
        sell_pct = ((sell_pct + 9) // 10) * 10
        sell_pct_count -= sell_pct
        sell_str += f"{sell_pct}%:" + key + "+"
        if sell_pct_count <= 0:
            break
    sell_str = sell_str[:-1] + "}"
    # print(sell_str)
    return sell_str


def sell_stock_support(period: str = "60", sell_pct: float = 1):
    global sell_symbol_data_dict
    isword_day, is_opening, is_Bidding = tdm.is_open_time()
    if not is_opening or tdm.debug_str == "debug":
        yfydm.push_wx_msg(WxMsgType.STRATEGY_SELL, "不在交易时间/debug,暂停交易,卖出失效")
        return

    stock_list = yfyam.get_all_account_pos_symbols()
    account_type_list = yfyam.account_types.keys()

    zbUtils.calc_resist_support(symbol_list=stock_list, period=period)
    for symbol in stock_list:
        symbol_rs_key = zbUtils.get_k_time_key(period=period, symbol=symbol, type="resistSupport")
        rs_list = zbUtils.resistSupport_dict[symbol_rs_key]
        resist_price = rs_list[0]
        support_price = rs_list[1]
        stock_simple_info = yfydm.get_stock_simple_info(symbol)
        close = stock_simple_info["close"].values[0]
        stock_name = stock_simple_info["name"].values[0]
        if close < support_price:  # tradePrice
            for account_type in account_type_list:
                stock_info = yfyam.get_account_stock_info(account_type=account_type, symbol=symbol)
                if "availableAmount" in stock_info and stock_info["availableAmount"] > 0:
                    msg = f"{symbol}-{stock_name}-跌破支撑线卖出股票，当前价{close}小于支撑线[{support_price}]"
                    yfyLog.logger.info(msg)
                    yfyam.sell_stock(account_type=account_type, symbol=symbol, sell_pct=sell_pct, msg=msg)


def sell_stock_strategy(buy_strategy, sell_scale):
    global sell_symbol_data_dict
    isword_day, is_opening, is_Bidding = tdm.is_open_time()
    if not is_opening or tdm.debug_str == "debug":
        yfydm.push_wx_msg(WxMsgType.STRATEGY_SELL, "不在交易时间/debug,暂停交易,卖出失效")
        return

    stock_list = yfyam.get_all_account_strategies_symbols(buy_strategy)
    account_type_list = yfyam.account_types.keys()
    now = datetime.datetime.now()
    today_tm_str = now.strftime("%Y-%m-%d")

    sell_pct = sell_scale
    for symbol in stock_list:
        stock_simple_info = yfydm.get_stock_simple_info(symbol)
        stock_name = stock_simple_info["name"].values[0]
        rank = stock_simple_info["rank"].values[0]
        if rank != 1:
            for account_type in account_type_list:
                stock_info = yfyam.get_account_stock_info(symbol=symbol)
                buy_tm_str = stock_info["tradeBuyTime"].split(" ")[0]
                if buy_tm_str != today_tm_str:
                    msg = f"{symbol}-{stock_name}--执行策略【{buy_strategy}】卖出,卖出比例：{sell_pct}"
                    yfyLog.logger.info(msg)
                    yfyam.sell_stock(account_type=account_type, symbol=symbol, sell_pct=sell_pct, msg=msg)


stock_ztzb_list = []  # 涨停炸板列表


def get_ztzb_pool(time_str=""):
    global stock_ztzb_list
    if time_str == "":
        time_str = datetime.datetime.now().strftime("%Y%m%d")  # ********
    stock_df = thirdDataMgr.get_stock_zt_pool_zbgc(time_str)
    if stock_df is not None and "代码" in stock_df.columns:
        stock_ztzb_list = stock_df["代码"].to_list()


def get_strategy_sell_info(symbol, strategy_type):
    stock_info = yfydm.get_stock_simple_info(symbol)
    if strategy_type == sconfig.FIRST10ZT_STRATEGY:
        return ""
    elif strategy_type == sconfig.FIRST20ZT_STRATEGY:
        return ""
    elif strategy_type == sconfig.BUY_SINGAL_STRATEGY:
        return ""
    elif strategy_type == sconfig.RANKRATIO_STRATEGY:
        return ""
    elif strategy_type == sconfig.TOPPOS_STRATEGY:
        return ""
    elif strategy_type == sconfig.TOPZHUJING_STRATEGY:
        return ""
    elif strategy_type == sconfig.FIVEMIN_SINGAL_STRATEGY:
        return ""
    else:
        return ""


UP_TREND_STOCK = "上升趋势票"
DOWN_TREND_STOCK = "下降趋势票"
SIDE_WAYS_STOCK = "横盘震荡票"
FS_FB_STOCK = "首板封板票"
ZT_FB_STOCK = "涨停封板票"
ZT_ZB_STOCK = "涨停炸板票"
GB_STOCK = "高标票"
DT_STOCK = "跌停票"
TREND_STOCK = "趋势票"


def get_sell_strategy(symbol):
    stock_info = yfydm.get_stock_simple_info(symbol)
    gq_sell_str = "#过期卖:{40%:2:-3%+20%:3:3%+30%:5:5%+40%:7:10%+30%:10:15%}"
    zz_sell_str = "#追踪卖:{卖出比例:65%+固定止损:15%+动态止损:7%+动态偏移:0%}"
    zy_sell_str = "#止盈卖:{20%:8%+30%:15%+40%:25%}"
    zd_sell_str = "#涨跌卖:{10cm=10%:3%+10%:5%+10%:7%+10%:9%&&20cm=10%:5%+10%:7%+10%:9%+10%:13%+10%:18%}"
    hl_sell_str = "#回落卖:{100%&&3%:5%:7%:9%:12%:15%}"
    symbol = stock_info["symbol"].values[0]
    close = stock_info["close"].values[0]
    is_20cm = symbol.startswith("30") | symbol.startswith("68")
    threeD_pct_chg = stock_info["changePercent3D"].values[0]
    fiveD_pct_chg = stock_info["changePercent5D"].values[0]
    tenD_pct_chg = stock_info["changePercent10D"].values[0]
    twentyD_pct_chg = stock_info["changePercent20D"].values[0]
    thirtyD_pct_chg = stock_info["changePercent30D"].values[0]
    is_zt_stock = stock_info["isLimitUp"].values[0]  # sUtils.is_daily_limit(symbol, is_up_limit=True)
    is_dt_stock = stock_info["isLimitDown"].values[0]  # sUtils.is_daily_limit(symbol, is_up_limit=False)
    is_fszt_stock = stock_info["lianbanCount"].values[0] == 1
    sell_strategy_str = gq_sell_str + zz_sell_str

    msg = SIDE_WAYS_STOCK
    # 大趋势票，下跌反转，上涨反转 "#50%:MA10+50%:MA30+50%:MA250+50%:EMA21+50%:EMA55"
    is_long_upTrend, is_long_downTrend, ema_dict = sUtils.check_stock_trend(symbol=symbol, win_count=20)
    if is_long_upTrend:
        u_zy_sell_str = "#止盈卖:{20%:15%+30%:30%+30%:60%}"
        sell_strategy_str = get_ema_sell_str(close, ema_dict) + u_zy_sell_str  # 长上升趋势，只按均线卖
        msg = UP_TREND_STOCK
    elif is_long_downTrend:
        d_zy_sell_str = "#止盈卖:{10%:8%+20%:15%+30%:25%+30%:35%}"
        sell_strategy_str += get_ema_sell_str(close, ema_dict) + d_zy_sell_str + hl_sell_str
        msg = DOWN_TREND_STOCK
    else:  # 横盘趋势
        sell_strategy_str += zy_sell_str
        sell_strategy_str += zd_sell_str
        sell_strategy_str += hl_sell_str
        msg = SIDE_WAYS_STOCK

    if is_zt_stock:  # 连板票
        zt_zz_sell_str = "#追踪卖:{卖出比例:70%+固定止损:3%+动态止损:5%+动态偏移:0%}"
        zt_zs_sell_str = "#止损卖:{100%&&-2%:-4%:-6%:-8%}"
        zt_zd_sell_str = "#涨跌卖:{10cm=10%:3%+5%:4%+10%:5%+5%:6%+10%:7%+10%:9+10%:9.84%&&20cm=10%:3%+10%:5%+10%:7%+10%:9%+10%:11%+10%:16%+10%:19.8%}"
        zt_jj_sell_str = "#竞价卖:{20%:-2%+10%:-3%+20%:-4%}"
        sell_strategy_str = zt_zz_sell_str + zt_zs_sell_str + zt_zd_sell_str + zt_jj_sell_str
        msg = ZT_FB_STOCK

    if is_fszt_stock:  # 首板票
        zt_zz_sell_str = "#追踪卖:{卖出比例:70%+固定止损:10%+动态止损:4%+动态偏移:0%}"
        zt_zs_sell_str = "#止损卖:{100%&&-4%:-6%:-8%:-10%}"
        zt_zd_sell_str = "#涨跌卖:{10cm=5%:3%+5%:4%+5%:5%+15%:6%+40%:9.5&&20cm=5%:3%+5%:5%+5%:7%+5%:9%+10%:11%+20%:16%+30%:19.8%}"
        zt_jj_sell_str = "#竞价卖:{10%:-2%+10%:-3%+20%:-4%}"
        sell_strategy_str = zt_zz_sell_str + zt_zs_sell_str + zt_zd_sell_str + zt_jj_sell_str
        msg = FS_FB_STOCK

    if fiveD_pct_chg > 35 or tenD_pct_chg > 55 or twentyD_pct_chg > 85 or thirtyD_pct_chg > 95:  # 高标
        sell_strategy_str = "#均线卖:{30%:MA3+50%:MA5+20%:MA10}#回落卖:{100%&&6%:7%:8%:9%:12%:15%}"
        if is_20cm:
            sell_strategy_str += "#竞价卖:{20%:-3%+20%:-4%+20%:-5%+20%:-6%}"
        else:
            sell_strategy_str += "#竞价卖:{20%:-2%+20%:-4%+30%:-6%}"
        msg = GB_STOCK

    if is_dt_stock:
        sell_strategy_str = hl_sell_str + gq_sell_str + zd_sell_str
        msg = DT_STOCK

    global stock_ztzb_list
    if symbol in stock_ztzb_list:  # 涨停炸板票
        zb_zy_sell_str = "#止盈卖:{20%:3%+30%:5%+40%:10%}"
        zb_hl_sell_str = "#回落卖:{100%&&1%:2%:3%:4%:5%:7%}"
        zb_zd_sell_str = "#涨跌卖:{10cm=20%:3%+20%:5%+10%:7%+20%:9%&&20cm=10%:2%+10%:4%+10%:6%+10%:8%+10%:10%+10%:12%+10%:14%+10%:18%}"
        zb_jj_sell_str = "#竞价卖:{30%:-2%+20%:-3%+20%:-4%+30%:-6%}"
        sell_strategy_str = gq_sell_str + zb_zy_sell_str + zb_zd_sell_str + zb_hl_sell_str + zb_jj_sell_str
        msg = ZT_ZB_STOCK

    return sell_strategy_str, msg


def get_zhibiao_data(symbol):
    now_time = datetime.datetime.now()
    start_date_time = now_time - datetime.timedelta(days=300)
    start_date_str = start_date_time.strftime('%Y-%m-%d %H:%M:%S')
    end_date_str = now_time.strftime("%Y-%m-%d %H:%M:%S")
    stockMgr.get_stock_kinfo(symbol=symbol, period="daily", start_date_str=start_date_str, end_date_str=end_date_str,
                             update_period_mins=90, adjust="qfq", symbol_list=[])
    stock_hist_df = stockMgr.stock_kinfo_dict[symbol + "_" + "daily"]
    df = pd.DataFrame()
    if not stock_hist_df.empty:
        df = stock_hist_df.copy()
        df["symbol"] = symbol
        thirdDataMgr.rename_stock_data_columns(df)
    open = df["open"].values
    close = df["close"].values
    high = df["high"].values
    low = df["low"].values
    df["ma_5"] = mt.MA(close, 5)
    df["ma_10"] = mt.MA(close, 10)
    df["ma_20"] = mt.MA(close, 20)
    df["hhv5"] = mt.HHV(high, 5)
    df["llv5"] = mt.LLV(low, 5)
    df['mid_price'] = (close - open) / 2 + open
    return df


def get_support_ema_sell_strategy(symbol):
    stock_simple_info = yfydm.get_stock_simple_info(symbol)

    rs_count = 13
    zbUtils.calc_resist_support(symbol_list=[symbol], period="60", rs_count=rs_count)
    symbol_rs_key = zbUtils.get_k_time_key(period="60", symbol=symbol, type="resistSupport")
    rs_list = zbUtils.resistSupport_dict[symbol_rs_key]
    resist_price = rs_list[0]
    support_price = rs_list[1]
    close = stock_simple_info["close"].values[0]
    stock_name = stock_simple_info["name"].values[0]

    df = get_zhibiao_data(symbol)
    top_10 = df.tail(10).nlargest(10, 'changePercent')
    max_change_row = top_10.loc[top_10['changePercent'].idxmax()]
    max_cmp_10 = (max_change_row['close'] - max_change_row['open']) / 2 + max_change_row['close']
    max_cmp_10 = round(max_cmp_10, 2)
    # print("max_cmp_10", max_change_row["date"])

    top_20 = df.tail(20).nlargest(20, 'changePercent')
    max_change_row = top_20.loc[top_20['changePercent'].idxmax()]
    max_cmp_20 = (max_change_row['close'] - max_change_row['open']) / 2 + max_change_row['close']
    max_cmp_20 = round(max_cmp_20, 2)
    # print("max_cmp_20", max_change_row["date"])

    ma_5, ma_10, hhv5, llv5 = round(df["ma_5"].iloc[-1], 2), round(df["ma_10"].iloc[-1], 2), df["hhv5"].iloc[-1], \
        df["llv5"].iloc[-1]
    p_list = [support_price, max_cmp_10, max_cmp_20, ma_5, ma_10, hhv5, llv5]

    # 过滤得到差距显著的值
    filtered_values = filter_significant_values(p_list, min_gap_percent=2, count=3)

    dj_sell_str = "#到价卖:{" + f"60%:-{filtered_values[0]}+20%:-{filtered_values[1]}+20%:-{filtered_values[2]}" + "}"
    return dj_sell_str


def get_ma_sell_price(symbol, period="15", ma_count=34):
    zbUtils.calc_ma_support(symbol_list=[symbol], period=period, rs_count=ma_count)
    symbol_rs_key = zbUtils.get_k_time_key(period=period, symbol=symbol, type="maSupport", d_count=str(ma_count))
    rs_list = zbUtils.resistSupport_dict[symbol_rs_key]
    support_price = rs_list[0]
    return support_price


def get_ma_sell_strategy(symbol):
    ma34_15m = get_ma_sell_price(symbol=symbol, period="15", ma_count=34)
    ma3_d = get_ma_sell_price(symbol=symbol, period="daily", ma_count=3)
    ma5_d = get_ma_sell_price(symbol=symbol, period="daily", ma_count=5)
    ma10_d = get_ma_sell_price(symbol=symbol, period="daily", ma_count=10)
    p_list = [ma34_15m, ma3_d, ma5_d, ma10_d]

    # 过滤得到差距显著的值
    filtered_values = filter_significant_values(p_list, min_gap_percent=2, count=3)

    dj_sell_str = "#到价卖:{" + f"60%:-{filtered_values[0]}+20%:-{filtered_values[1]}+20%:-{filtered_values[2]}" + "}"
    return dj_sell_str


def set_sell_strategies2(account_type, symbol, profit):
    get_ztzb_pool()

    stock_info = yfydm.get_stock_simple_info(symbol)
    pct_chg = stock_info["changePercent"].values[0]
    threeD_pct_chg = stock_info["changePercent3D"].values[0]
    fiveD_pct_chg = stock_info["changePercent5D"].values[0]
    tenD_pct_chg = stock_info["changePercent10D"].values[0]
    twentyD_pct_chg = stock_info["changePercent20D"].values[0]
    thirtyD_pct_chg = stock_info["changePercent30D"].values[0]

    msg = TREND_STOCK
    a_stock_info = yfyam.get_account_stock_info(account_type=account_type, symbol=symbol)
    strategy_type_list = str(a_stock_info["strategyInfo"]).split(",")
    zb_zy_sell_str = "#止盈卖:{15%:8%+15%:10%+30%:10%+50%:15%}#过期卖:{50%:3:-6%+50%:5:3%+100%:7:6%}"
    fzd_sell_str, fhl_sell_str = "", ""
    if profit < -0.01:  # 亏损的，早点卖掉
        fzd_sell_str1 = "#涨跌卖:{10cm=5%:<-2%+20%:<-4%+30%:<-5%+100%:<-6%+5%:>3%+10%:>4%+10%:>5%+10%:>6+10%:>7%+10%:>9.8%&&"
        fzd_sell_str2 = "20cm=5%:<-2%+20%:<-5%+30%:<-7%+100%:<-9%+5%:>4%+10%:>6%+20%:>8%+20%:>12%+20%:>19.85%,useOpen=True}"
        fzd_sell_str = fzd_sell_str1 + fzd_sell_str2
        fhl_sell_str = "#回落卖:{50%&&2%:4%:6%:8%:10%:12%}"

    if sconfig.USER_SOFTWARE_BUY in strategy_type_list or len(strategy_type_list) == 0:  # 人工买的，放宽一下逻辑
        zb_zy_sell_str = "#止盈卖:{10%:7%+10%:10%+20%:15%+40%:30%+30%:50%}"

    if threeD_pct_chg > 24 or tenD_pct_chg > 55 or twentyD_pct_chg > 70:
        zd_sell_str = fzd_sell_str if len(
            fzd_sell_str) > 0 else "#涨跌卖:{10cm=10%:>7%+10%:>9.8%&&20cm=10%:>12%+20%:>19.85%}"
        zz_sell_str = "#追踪卖:{卖出比例:30%+固定止损:15%+动态止损:8%+动态偏移:0%}"
        hl_sell_str = "#回落卖:{30%&&3%:5%:7%:9%:12%:15%}"
        msg = GB_STOCK
        sell_strategy_str = zz_sell_str + zd_sell_str + hl_sell_str + get_ma_sell_strategy(symbol)
    else:
        sell_strategy_str = zb_zy_sell_str + fzd_sell_str + fhl_sell_str + get_support_ema_sell_strategy(symbol)

    if pct_chg > 9.8:
        if symbol.startswith("30"):
            jj_sell_str = "#竞价卖:{15%:<-3%+40%:<-5%+100%:<-7%}"
        else:
            jj_sell_str = "#竞价卖:{15%:<-2%+40%:<-4%+100%:<-6%}"
        sell_strategy_str += jj_sell_str

    user_control_symbols = sconfig.get_user_control_symbols()
    if symbol in user_control_symbols:
        zs_sell_str = "#止损卖:{100%&&-35%:-50%:-75%:-99%}#涨跌卖:{10cm=50%:<-7%+100%:<-9.5%&&20cm=30%:<-8%+50%:<-12%+100%:<-15%}#回落卖:{100%&&7%:9%:11%:13%:15%:18%}"
        symbol_sell_str = sconfig.get_symbols_sell_strategy(symbol)
        if symbol_sell_str is not None and len(symbol_sell_str) > 0:
            zs_sell_str += symbol_sell_str
        sell_strategy_str, msg = f"{zs_sell_str}", "人工管理"

    if sconfig.EMA_AUTO_BUY not in strategy_type_list and sconfig.PCT_AUTO_BUY not in strategy_type_list:
        yfyam.set_sell_strategy(account_type=account_type, symbol=symbol, sell_strategy_types=sell_strategy_str,
                                msg=msg)


db_str_list = ["打10cm首板", "打10cm二板", "打10cm三板", "打10cmGC首板", "打10cmGC二板", "打Env10cm涨停", "打20cm首板",
               "破压力位10cm首板", "打10cm五板", "打10cmGC首板Test"]


def set_sell_strategies(account_type, symbol, profit):
    get_ztzb_pool()

    stock_info = yfydm.get_stock_simple_info(symbol)
    pct_chg = stock_info["changePercent"].values[0]
    threeD_pct_chg = stock_info["changePercent3D"].values[0]
    fiveD_pct_chg = stock_info["changePercent5D"].values[0]
    tenD_pct_chg = stock_info["changePercent10D"].values[0]
    twentyD_pct_chg = stock_info["changePercent20D"].values[0]
    thirtyD_pct_chg = stock_info["changePercent30D"].values[0]

    msg = TREND_STOCK

    db_str_set = set(db_str_list)
    a_stock_info = yfyam.get_account_stock_info(account_type=account_type, symbol=symbol)
    strategy_type_list = str(a_stock_info["strategyInfo"]).split(",")
    zb_zy_sell_str = "#止盈卖:{10%:7%+20%:9.8%+30%:20%+30%:35%}"
    zz_sell_str = "#追踪卖:{卖出比例:100%+固定止损:15%+动态止损:5%+动态偏移:12%}"
    gq_sell_str = "#过期卖:{30%:2:-6%+40%:3:-3%+40%:5:3%+100%:7:6%}"
    if threeD_pct_chg > 24 or tenD_pct_chg > 55 or twentyD_pct_chg > 70:
        zb_zy_sell_str = "均线卖:{30%:MA3+30%:MA5+40%:MA10}"  # 不做止盈，只做均线止损
        gq_sell_str = "#过期卖:{50%:2:3%+100%:3:5%+100%:5:10%}"
        msg = GB_STOCK

    if sconfig.USER_SOFTWARE_BUY in strategy_type_list or len(strategy_type_list) == 0:  # 人工买的，放宽一下逻辑
        zb_zy_sell_str = "#止盈卖:{10%:15%+20%:25%+40%:40%}"
        gq_sell_str = "#过期卖:{30%:3:-10%+40%:5:-15%+100%:10:10%}"
        msg = "人工管理"

    # 如果是打板策略，那么就只做23%的固定止损
    if db_str_set & set(strategy_type_list):
        zz_sell_str = "#追踪卖:{卖出比例:100%+固定止损:8%+动态止损:5%+动态偏移:12%}"
        gq_sell_str = "#过期卖:{50%:2:5%+100%:3:10%+100%:5:20%}"
        msg = "打板票"

    user_control_symbols = sconfig.get_user_control_symbols()
    if symbol in user_control_symbols:
        zb_zy_sell_str = "#止损卖:{100%&&-35%:-50%:-75%:-99%}"  # 不设置止盈，只设置止损
        zz_sell_str = sconfig.get_symbols_sell_strategy(symbol)
        gq_sell_str = ""
        msg = "人工管理"

    sell_strategy_str = zb_zy_sell_str + zz_sell_str + gq_sell_str
    if sconfig.EMA_AUTO_BUY not in strategy_type_list and sconfig.PCT_AUTO_BUY not in strategy_type_list:
        # account_status = sconfig.get_account_status(account_type)
        # if account_status == "open":  # 开门营业
        yfyam.set_sell_strategy(account_type=account_type, symbol=symbol, sell_strategy_types=sell_strategy_str,
                                msg=msg)


def get_dynamic_time(base_minutes, market_score):
    """
    根据市场强度动态调整时间
    :param base_minutes: 基础时间（分钟）
    :param market_score: 市场得分（0-100）
    :return: 调整后的时间（分钟）
    """
    try:
        # 参数类型检查
        if not isinstance(market_score, (int, float)) or not isinstance(base_minutes, (int, float)):
            return base_minutes
        
        # 限制分数范围
        market_score = max(0, min(100, float(market_score)))
        
        # 限制时间范围（9:30-15:00）
        if base_minutes < 9 * 60 + 30 or base_minutes > 15 * 60:
            return base_minutes
        
        if market_score >= 65:  # 强市调整
            if base_minutes <= 9 * 60 + 35:  # 9:35
                return min(base_minutes + 5, 15 * 60)  # 延长5分钟到9:40
            elif base_minutes <= 9 * 60 + 45:  # 9:45
                return min(base_minutes + 10, 15 * 60)  # 延长10分钟到9:55
            elif base_minutes <= 10 * 60 + 5:  # 10:05
                return min(base_minutes + 15, 15 * 60)  # 延长15分钟到10:20
            else:  # 11:05
                return min(base_minutes + 25, 15 * 60)  # 延长25分钟到11:30
        elif market_score <= 55:  # 弱市调整
            if base_minutes <= 9 * 60 + 35:  # 9:35
                return max(base_minutes - 2, 9 * 60 + 30)  # 提前2分钟到9:33，但不早于9:30
            elif base_minutes <= 9 * 60 + 45:  # 9:45
                return max(base_minutes - 8, 9 * 60 + 30)  # 提前8分钟到9:37
            elif base_minutes <= 10 * 60 + 5:  # 10:05
                return max(base_minutes - 15, 9 * 60 + 30)  # 提前15分钟到9:50
            else:  # 11:05
                return max(base_minutes - 20, 9 * 60 + 30)  # 提前20分钟到10:45
        return base_minutes  # 普通行情不调整
    except Exception as e:
        yfyLog.logger.error(f"动态时间调整出错: {e}")
        return base_minutes  # 出错时返回原始时间

def buy_signal_sell_strategy(account_type, symbol, test_mode=False,strategy_type=None):
    try:
        # 初始化Redis管理器
        redis_mgr = StockRedisMgr()
        
        # 1. 检查持仓
        a_stock_info = yfyam.get_account_stock_info(account_type=account_type, symbol=symbol)
        if not a_stock_info:
            return
        
        # 增强数据验证
        available_amount = a_stock_info.get("availableAmount", 0)
        if available_amount <= 0:
            return
        
        # 确保关键字段存在
        required_fields = ["tradePrice", "tradeAmount", "positionGold", "profit"]
        for field in required_fields:
            if field not in a_stock_info:
                yfyLog.logger.warning(f"[{symbol}] 缺少必要字段: {field}")
                return
        
        # 2. 获取股票信息
        stock_df = yfydm.get_stock_simple_info(symbol)
        if stock_df is None or stock_df.empty:
            yfyLog.logger.warning(f"[{symbol}] 无法获取股票数据")
            return
        
        # 3. 检查交易时间（测试模式下跳过）
        is_word_day, is_opening, is_Bidding = tdm.is_open_time()
        if not test_mode:
            if not is_opening:
                return
            
        now = datetime.datetime.now()
        try:
            profit = float(a_stock_info["profit"])
            positionGold = float(a_stock_info["positionGold"])
            tradePrice = float(a_stock_info["tradePrice"])
            tradeAmount = float(a_stock_info["tradeAmount"])
            # 修复字段名：使用正确的字段名
            tradeBuyTime = a_stock_info.get("tradeBuyTime", "")  # 第一次开仓时间
            latestBuyTradeTime = a_stock_info.get("latestBuyTradeTime", "")  # 最近补仓时间
            
            if tradeAmount <= 0:
                yfyLog.logger.warning(f"[{symbol}] 交易数量异常: {tradeAmount}")
                return
            if positionGold <= 0:
                yfyLog.logger.warning(f"[{symbol}] 持仓金额异常: {positionGold}")
                return
            
            close = stock_df["close"].values[0]
            high = stock_df["high"].values[0]
            ma3 = stock_df["ma3"].values[0]
            ma5 = stock_df["ma5"].values[0]
            ma10 = stock_df["ma10"].values[0]
            hhv3 = stock_df["hhv3"].values[0]
            hhv5 = stock_df["hhv5"].values[0]
            hhv10 = stock_df["hhv10"].values[0]
            llv10 = stock_df["llv10"].values[0]
            changePercent3D = stock_df["changePercent3D"].values[0]
            changePercent5D = stock_df["changePercent5D"].values[0]
            isLimitUp = stock_df["isLimitUp"].values[0]
                
        except (ValueError, IndexError, KeyError, TypeError) as e:
            yfyLog.logger.error(f"获取股票数据出错: {e}")
            return

        # 构建Redis键 - 增加账户类型确保唯一性
        sell_key = redis_mgr.build_key(RedisKeyPrefix.TRADE_STOCK_SELL.value, account_type, symbol)
        # 计算盈利率（已在上面验证过tradePrice > 0）
        profit_pct = close / tradePrice
        # 检查Redis中是否已有记录
        sell_record = redis_mgr.get_value(sell_key)
        
        # 如果Redis中有记录，使用Redis中的真实买入价格（避免减仓后价格计算异常）
        if sell_record is not None:
            redis_trade_price = sell_record.get("trade_price")
            if redis_trade_price and redis_trade_price > 0:
                yfyLog.logger.info(f"[{symbol}] 使用Redis中的历史买入价格: {redis_trade_price:.2f}，账户返回价格: {tradePrice:.2f}")
                tradePrice = redis_trade_price
                # 重新计算盈利率
                profit_pct = close / tradePrice
        
        # 处理Redis记录和18%卖出逻辑
        if sell_record is None:
            # 没有Redis记录，先创建记录
            base_info = {
                "symbol": symbol,
                "account_type": account_type,
                "trade_price": tradePrice,
                "trade_amount": tradeAmount,
                "trade_buy_time": tradeBuyTime,  # 第一次开仓时间
                "latest_buy_trade_time": latestBuyTradeTime,  # 最近补仓时间
                "position_gold": positionGold,
                "record_time": now.strftime("%Y-%m-%d %H:%M:%S"),
                "half_sold": False,  # 是否已卖出一半
                "profit_18_sold": False,  # 是否已执行18%盈利卖出
                "profit_30_sold": False   # 是否已执行30%盈利卖出
            }
            
            # 记录基础信息到Redis，设置10天过期
            redis_mgr.set_value(sell_key, base_info, expire=10*24*3600)
            yfyLog.logger.info(f"[{symbol}] 记录股票基础信息到Redis: 买入价{tradePrice:.2f}, 开仓时间{tradeBuyTime}, 最近补仓时间{latestBuyTradeTime}")
            
            # 如果创建记录时已经满足18%盈利，设置为刚创建的记录以便后续处理
            if profit_pct >= 1.18:
                yfyLog.logger.info(f"[{symbol}] 新记录创建时已达到18%盈利({(profit_pct-1)*100:.2f}%)，将执行卖出")
                sell_record = base_info  # 使用刚创建的记录
            # 不再直接return，继续执行后续的卖出条件检查
        
        # 涨停不卖出
        if isLimitUp:
            yfyLog.logger.info(f"[{symbol}] 涨停不卖出")
            return

        # 检查是否达到18%盈利且未卖出一半
        if profit_pct >= 1.18 and sell_record is not None and not sell_record.get("profit_18_sold", False):
            # 卖出一半
            sell_pct = 0.5
            msg = f"盈利达到18%，卖出一半！当前盈利: {(profit_pct-1):.2%}，买入价:{tradePrice:.2f}，当前价:{close:.2f}"
            yfyLog.logger.info(f"[{symbol}] {msg}")
            
            try:
                # 执行卖出操作
                sell_result = yfyam.sell_stock(account_type=account_type, symbol=symbol, sell_pct=sell_pct, msg=msg)
                
                if sell_result:  # 卖出成功
                    # 更新Redis记录，标记已卖出一半
                    sell_record["half_sold"] = True
                    sell_record["profit_18_sold"] = True
                    sell_record["sell_half_time"] = now.strftime("%Y-%m-%d %H:%M:%S")
                    sell_record["sell_half_price"] = close
                    sell_record["sell_half_profit_pct"] = round((profit_pct - 1) * 100, 2)
                    redis_mgr.set_value(sell_key, sell_record, expire=30*24*3600)
                    yfyLog.logger.info(f"18%盈利卖出一半完成，已更新Redis记录")
                    
                    # 更新持仓信息
                    yfyam.get_account_datainfo(account_type=account_type)
                else:
                    yfyLog.logger.error(f"[{symbol}] 卖出操作返回失败")
                    
                return  # 执行完18%卖出后返回，等下次调用时再检查30%
                
            except Exception as e:
                yfyLog.logger.error(f"{account_type}:{symbol} 执行18%盈利卖出失败: {e}")
                return

        # 检查是否达到30%盈利且已执行18%卖出但未执行30%卖出
        if (profit_pct >= 1.30 and sell_record is not None and 
            sell_record.get("profit_18_sold", False) and 
            not sell_record.get("profit_30_sold", False)):
            
            # 卖出剩余仓位的一半
            sell_pct = 0.5
            msg = f"盈利达到30%，再卖出一半！当前盈利: {(profit_pct-1):.2%}，买入价:{tradePrice:.2f}，当前价:{close:.2f}"
            yfyLog.logger.info(f"[{symbol}] {msg}")
            
            try:
                # 执行卖出操作
                sell_result = yfyam.sell_stock(account_type=account_type, symbol=symbol, sell_pct=sell_pct, msg=msg)
                
                if sell_result:  # 卖出成功
                    # 更新Redis记录，标记已执行30%卖出
                    sell_record["profit_30_sold"] = True
                    sell_record["sell_30_time"] = now.strftime("%Y-%m-%d %H:%M:%S")
                    sell_record["sell_30_price"] = close
                    sell_record["sell_30_profit_pct"] = round((profit_pct - 1) * 100, 2)
                    redis_mgr.set_value(sell_key, sell_record, expire=30*24*3600)
                    yfyLog.logger.info(f"30%盈利卖出一半完成，已更新Redis记录")
                    
                    # 更新持仓信息
                    yfyam.get_account_datainfo(account_type=account_type)
                else:
                    yfyLog.logger.error(f"[{symbol}] 30%盈利卖出操作返回失败")
                    
                return  # 执行完30%卖出后直接返回
                
            except Exception as e:
                yfyLog.logger.error(f"{account_type}:{symbol} 执行30%盈利卖出失败: {e}")
                return

        hl3_pct = round((hhv3 - close) / hhv3 * 100, 2) if hhv3 > 0 else 0
        hl5_pct = round((hhv5 - close) / hhv5 * 100, 2) if hhv5 > 0 else 0
        hl10_pct = round((hhv10 - close) / hhv10 * 100, 2) if hhv10 > 0 else 0

        is_sell = False
        msg = ""

        zs_pct = 0.9
        if strategy_type == sconfig.XIAOGAOPOOL_250_STRATEGY or strategy_type == sconfig.XIAOGAOPOOL_60_STRATEGY:
            zs_pct = 0.948

        # 条件1: 亏损超过10%止损
        if profit_pct < zs_pct and tradePrice > 0:
            is_sell = True
            msg = f"亏损超过{zs_pct*100}%止损，当前盈利: {(profit_pct-1):.2%}，卖出!"

        sell_pct = 1
        # 条件2: 盈利5%以上的均线卖出
        if profit_pct > 1.05 and not is_sell:
            if high > ma5*1.08 and close < ma3*0.992:  # 距离5日均线还很远，跌破3日就卖
                is_sell = True
                msg = f"盈利大于5%，且回落幅度大，当前盈利: {(profit_pct-1):.2%}，跌破3日均线(MA3:{ma3:.2f})，卖出!"
            elif close < ma5*0.99:  # 跌破5日均线
                is_sell = True
                msg = f"盈利大于5%，当前盈利: {(profit_pct-1):.2%}，跌破5日均线(MA5:{ma5:.2f})，卖出!"
        
        # 条件3: 盈利12%以上跌破10日均线
        if profit_pct > 1.12 or tradePrice < 0:
            if close < ma10*0.99:
                is_sell = True
                msg = f"盈利大于12%，当前盈利: {(profit_pct-1):.2%}，跌破10日均线(MA10:{ma10:.2f})，卖出!"
        
        # 条件4: 回落卖出(亏损不超过2%时启用)
        if profit_pct > 0.98 and (hl3_pct > 6 or hl5_pct > 8) and not is_sell:
            is_sell = True
            msg = f"回落保护，当前盈利: {(profit_pct-1):.2%}，3日回落:{hl3_pct:.2f}%,5日回落:{hl5_pct:.2f}%，卖出!"

        # 条件5: 买入位置相对安全但现在跌到危险位置的止损
        # 逻辑：买入时比10日最低价高3%以上（相对安全），现在接近10日最低价（危险），且亏损超过5%
        if now.hour >= 14 and now.minute >= 50:
            if tradePrice > llv10 * 1.03 and close <= llv10 * 1.01 and profit_pct < 0.95:
                is_sell = True
                msg = f"从安全位置跌到危险位置止损，当前盈利: {(profit_pct-1):.2%}，买入价:{tradePrice:.2f}，当前价:{close:.2f}，10日最低:{llv10:.2f}，卖出!"

        cg_3d = -5
        cg_5d = -3
        if strategy_type == sconfig.XIAOGAOPOOL_60_STRATEGY:
            cg_3d = -3
            cg_5d = 0
        if strategy_type == sconfig.EMA_BUY_SINGAL_STRATEGY:
            cg_3d = -5
            cg_5d = -1
        #条件6：3日涨跌低于5%，5日涨跌低于3%
        if changePercent3D < cg_3d or changePercent5D < cg_5d:
            is_sell = True
            msg = f"3日涨跌{changePercent3D:.2f}%-低于{cg_3d}，5日涨跌{changePercent5D:.2f}%-低于{cg_5d}，当前盈利: {(profit_pct-1):.2%}，卖出!"

        # 5. 执行卖出
        if is_sell:
            yfyLog.logger.info(f"[{symbol}] {msg}")
            try:
                sell_result = yfyam.sell_stock(account_type=account_type, symbol=symbol, sell_pct=sell_pct, msg=msg)
                yfyam.get_account_datainfo(account_type=account_type)  # 更新持仓
                
                # 全仓卖出后清除Redis记录，避免下次重新买入时出现问题
                if sell_result and sell_pct == 1:
                    redis_mgr.delete_keys(sell_key)
                    yfyLog.logger.info(f"全仓卖出完成，已清除Redis记录")
                        
            except Exception as e:
                yfyLog.logger.error(f"{account_type}:{symbol} 执行卖出操作失败: {e}")

    except Exception as e:
        yfyLog.logger.error(f"{account_type}:{symbol} 买信号卖出策略执行出错: {e}")

'''
def db_stock_sell_strategy(account_type, symbol, jj_pct = -0.05): 
    try:
        # 1. 检查持仓
        a_stock_info = yfyam.get_account_stock_info(account_type=account_type, symbol=symbol)
        if not a_stock_info or a_stock_info.get("availableAmount", 0) <= 0:
            return

        # 2. 获取股票信息
        stock_df = yfydm.get_stock_simple_info(symbol)
        if stock_df is None or stock_df.empty:
            return
        
         # 4. 检查交易时间
        now = datetime.datetime.now()
        
        if now.hour < 9 or now.hour >= 15:  # 交易时间检查
            return
        current_minutes = now.hour * 60 + now.minute
        s_base_time = 9 * 60 + 28  # 09:28
        if current_minutes < s_base_time:
            return
        
        all_info_dict = yfydm.get_all_info_dict()
        
        # 3. 获取基础数据
        try:
            kp_chg_pct = float(stock_df["openChangePercent"].values[0])
            chg_pct = float(stock_df["changePercent"].values[0])
            is_up_limit = bool(stock_df["isLimitUp"].values[0])
            is_down_limit = bool(stock_df["isLimitDown"].values[0])
            # 添加量能指标
            volume_ratio = float(stock_df["volume_ratio_3d"].values[0])  # 获取量比
            turnover_rate = float(stock_df["turnOverRate"].values[0])  # 获取换手率
            mainInFlowAmount = stock_df["mainInFlowAmount"].values[0]
            kpl_mainInFlowAmount = stock_df["kplMainInFlowAmount"].values[0]
            high = stock_df["high"].values[0]
            low = stock_df["low"].values[0]
            close = stock_df["close"].values[0]
            amount = stock_df["volumeAmount"].values[0]
            #回落和开盘间隔
            l_gap_pct = round((close - low) / low * 100, 2)
            hl_pct = round((high - close) / high * 100, 2)
            gap_chg_pct = chg_pct - kp_chg_pct
            am_pct = round(max(mainInFlowAmount/amount,kpl_mainInFlowAmount/amount), 2)
            #
            than_zero_cnt = all_info_dict["than_zero_cnt"]
            total_cnt = all_info_dict["total_cnt"]
            than_zero_pct = than_zero_cnt / total_cnt if total_cnt > 0 else 0

            #  
        except (ValueError, IndexError) as e:
            yfyLog.logger.error(f"获取股票数据出错: {e}")
            return

        # 5. 获取市场强度评分
        try:
            market_score_info = fpm.calculate_position_score()
            market_score = (market_score_info.get("score_details", {}).get("total_score", 50) 
                          if isinstance(market_score_info, dict) else 50)
        except Exception as e:
            yfyLog.logger.error(f"获取市场强度评分失败: {e}")
            market_score = 50  # 出错时使用中性分数

        msg = ""
        is_sell = False

        #集合竞价卖
        if not is_up_limit:
            base_time = 9 * 60 + 28  # 09:28
            base_time_1 = 9 * 60 + 30  # 09:30
            base_time_2 = 9 * 60 + 33  # 09:33
            base_time_3 = 9 * 60 + 35  # 09:35
            if current_minutes >= base_time and current_minutes < base_time_1:
                if kp_chg_pct < 5 and am_pct < jj_pct and than_zero_pct < 0.6:# 竞价主力流入占比成交额小于5%
                    if not symbol.startswith("30") and not symbol.startswith("68"):#30和68开头的股票不卖
                        msg = f"集合竞价卖(开盘{kp_chg_pct:.2f}%,净额占比{am_pct}, {now.strftime('%H:%M')})"
                        is_sell = True
            elif current_minutes >= base_time_1 and current_minutes < base_time_2:#3分钟内
                if am_pct > 0:# 主力流入占比成交额大于0%
                   return
            elif current_minutes >= base_time_1 and current_minutes < base_time_3:#5分钟内
                if am_pct > 0.1:# 主力流入占比成交额大于10%
                    return
        
        # 6. 执行卖出策略
        # 6.1 平开（-2% <= 开盘涨幅 <= 2%)
        if abs(kp_chg_pct) <= 2:
            if chg_pct < -4:  # 平开后大幅下跌
                msg = f"平开后大幅下跌(开盘{kp_chg_pct:.2f}%,当前跌幅{chg_pct:.2f}%)"
                is_sell = True
            if not is_up_limit:  # 未涨停超时
                base_time = 9 * 60 + 45  # 9:45
                sell_time = get_dynamic_time(base_time, market_score)
                if current_minutes >= sell_time:
                    msg = f"平开未涨停超时(开盘{kp_chg_pct:.2f}%,当前涨幅{chg_pct:.2f}%,{now.strftime('%H:%M')})"
                    is_sell = True
                if gap_chg_pct > 4 or hl_pct > 2 or l_gap_pct > 4:#挣钱了赶紧卖出
                    msg = f"平开挣钱了(开盘{kp_chg_pct:.2f}%,涨幅{chg_pct:.2f}%,开间{gap_chg_pct:.2f},回落{hl_pct:.2f}%,爬升{l_gap_pct:.2f})"
                    is_sell = True

        # 6.2 低开（< -2%)
        elif kp_chg_pct < -2:
            if chg_pct < -5 and not is_down_limit:  # 低开且低走,未跌停
                msg = f"低开低走(开盘{kp_chg_pct:.2f}%,当前跌幅{chg_pct:.2f}%),未跌停"
                is_sell = True
            if not is_up_limit:  # 没有涨停，超时了也卖掉
                base_time = 9 * 60 + 45  # 9:45
                sell_time = get_dynamic_time(base_time, market_score)
                if current_minutes >= sell_time and gap_chg_pct < 6:
                    msg = f"低开高走超时(开盘跌幅{kp_chg_pct:.2f}%,当前涨幅{chg_pct:.2f}%,{now.strftime('%H:%M')})"
                    is_sell = True
                if gap_chg_pct > 6 or hl_pct > 2 or l_gap_pct > 6:#挣钱了赶紧卖出
                    msg = f"低开高走(开盘跌幅{kp_chg_pct:.2f}%,当前涨幅{chg_pct:.2f}%,开间{gap_chg_pct:.2f},回落{hl_pct:.2f}%,爬升{l_gap_pct:.2f})"
                    is_sell = True

        # 6.3 大幅高开不秒板
        elif kp_chg_pct > 4:
            # 超大幅高开的情况优先处理
            if kp_chg_pct > 7:
                if gap_chg_pct < -3:  # 高开回落幅度大
                    msg = f"超大幅高开回落(开盘涨幅{kp_chg_pct:.2f}%,当前涨幅仅{chg_pct:.2f}%)"
                    is_sell = True
            
            if not is_up_limit:  # 未涨停超时
                base_time = 9 * 60 + 35  # 9:35
                sell_time = get_dynamic_time(base_time, market_score)
                if current_minutes >= sell_time:
                    msg = f"高开不秒板(开盘涨幅{kp_chg_pct:.2f}%,{now.strftime('%H:%M')})"
                    is_sell = True

        # 6.4 普通高开（2-4%）
        elif kp_chg_pct > 2:
            if chg_pct < -1:  # 高开低走且非跌停
                msg = f"高开低走(开盘涨幅{kp_chg_pct:.2f}%,当前跌幅{chg_pct:.2f}%)"
                is_sell = True
            if not is_up_limit:  # 没有涨停，超时了也卖掉
                base_time = 10 * 60 + 5  # 10:05
                sell_time = get_dynamic_time(base_time, market_score)
                if current_minutes >= sell_time and chg_pct < 6:
                    msg = f"高开高走超时(开盘涨幅{kp_chg_pct:.2f}%,当前涨幅{chg_pct:.2f}%,{now.strftime('%H:%M')})"
                    is_sell = True
        
        
         # 6.6 量能判断（所有情况通用）
        elif not is_up_limit and current_minutes > (9 * 60 + 45):  # 9:45后才判断量能
            if volume_ratio < 0.8:  # 量比过低
                msg = f"量能不足(量比{volume_ratio:.2f},换手率{turnover_rate:.2f}%)"
                is_sell = True
            elif turnover_rate < 2 and chg_pct < 5:  # 换手率过低且涨幅不足
                msg = f"换手率过低(换手率{turnover_rate:.2f}%,涨幅{chg_pct:.2f}%)"
                is_sell = True

        # 6.5 兜底条件
        if not is_up_limit:
            base_time = 11 * 60 + 5  # 11:05
            sell_time = get_dynamic_time(base_time, market_score)
            if current_minutes >= sell_time and chg_pct < 5:
                msg = f"兜底条件({now.strftime('%H:%M')}未涨停)"
                is_sell = True
            base_time = 14 * 60 + 52  # 14:55
            sell_time = get_dynamic_time(base_time, market_score)
            if current_minutes >= sell_time:
                msg = f"未涨停无条件({now.strftime('%H:%M')})卖出"
                is_sell = True
        
        # 7. 执行卖出
        if is_sell:
            yfyLog.logger.info(f"[{symbol}] {msg}")
            try:
                yfyam.sell_stock(account_type=account_type, symbol=symbol, sell_pct=1, msg=msg)
                yfyam.get_account_datainfo(account_type=account_type)  # 更新持仓
            except Exception as e:
                yfyLog.logger.error(f"执行卖出操作失败: {e}")

    except Exception as e:
        yfyLog.logger.error(f"打板卖出策略执行出错: {e}")
'''


def db_stock_sell_strategy2(account_type, symbol, sell_pct=1.0,jj_pct=-0.5,
                            pullback_pct_from_intraday_high: float = 0.025 # 从日内高点回落卖出阈值，默认2.5%
                        ): 
    """
    打板隔天卖出策略 (中文版):
    1. 基于 check_mPrice_sell 的核心逻辑判断 (快速下跌、持续低于均线等)。
    2. 从日内高点回落一定百分比则卖出。
    3. 时间窗口控制：14:53未涨停则强制卖出。
    """
    try:
        # 1. 检查持仓
        a_stock_info = yfyam.get_account_stock_info(account_type=account_type, symbol=symbol)
        if not a_stock_info or a_stock_info.get("availableAmount", 0) <= 0:
            # yfyLog.logger.info(f"[{account_type},{symbol}] 打板卖出策略2,无持仓或可用数量为0。")
            return
        
        now = datetime.datetime.now() # 定义 now
        all_info_dict = yfydm.get_all_info_dict()
        stock_df = yfydm.get_stock_simple_info(symbol)

        if stock_df is None or stock_df.empty:
            yfyLog.logger.warning(f"[{account_type},{symbol}] 打板卖出策略2, 获取stock_simple_info失败或为空.")
            return
        
        # 3. 获取基础数据
        try:
            kp_chg_pct = float(stock_df["openChangePercent"].values[0])
            chg_pct = float(stock_df["changePercent"].values[0])
            is_up_limit = bool(stock_df["isLimitUp"].values[0])
            is_down_limit = bool(stock_df["isLimitDown"].values[0])
            # 添加量能指标
            volume_ratio = float(stock_df["volume_ratio_3d"].values[0])  # 获取量比
            turnover_rate = float(stock_df["turnOverRate"].values[0])  # 获取换手率
            mainInFlowAmount = stock_df["mainInFlowAmount"].values[0]
            kpl_mainInFlowAmount = stock_df["kplMainInFlowAmount"].values[0]
            high = stock_df["high"].values[0]
            low = stock_df["low"].values[0]
            close = stock_df["close"].values[0]
            amount = stock_df["volumeAmount"].values[0]
            #回落和开盘间隔
            l_gap_pct = round((close - low) / low * 100, 2)
            hl_pct = round((high - close) / high * 100, 2)
            gap_chg_pct = chg_pct - kp_chg_pct
            am_pct = 0
            if amount > 0:
                am_pct = round(max(mainInFlowAmount/amount,kpl_mainInFlowAmount/amount), 2)
            #
            than_zero_cnt = all_info_dict["than_zero_cnt"]
            total_cnt = all_info_dict["total_cnt"]
            than_zero_pct = than_zero_cnt / total_cnt if total_cnt > 0 else 0
        except (ValueError, IndexError, KeyError) as e:
            yfyLog.logger.error(f"获取股票数据出错: {e}")
            return
        
        current_minutes = now.hour * 60 + now.minute
        s_base_time = 9 * 60 + 28  # 09:28
        e_base_time = 14 * 60 + 58  # 14:58
        if current_minutes < s_base_time or current_minutes > e_base_time:
            pass#return

        sell_reason = "" 
        should_skip_further_checks = False
        if not is_up_limit:
            base_time = 9 * 60 + 28  # 09:28
            base_time_1 = 9 * 60 + 30  # 09:30
            base_time_2 = 9 * 60 + 33  # 09:33
            base_time_3 = 9 * 60 + 35  # 09:35
            if current_minutes >= base_time and current_minutes < base_time_1:
                if kp_chg_pct < 5 and am_pct < jj_pct and than_zero_pct < 0.6 and kpl_mainInFlowAmount < -3000000:# 竞价主力流入占比成交额小于5%
                    if not symbol.startswith("30") and not symbol.startswith("68"):#30和68开头的股票不卖
                        sell_reason = f"集合竞价卖(开盘{kp_chg_pct:.2f}%,净额占比{am_pct}, {now.strftime('%H-%M')}，主力流入{mainInFlowAmount})"
            elif current_minutes >= base_time_1 and current_minutes < base_time_2:#3分钟内
                if am_pct > 0:# 主力流入占比成交额大于0%
                    should_skip_further_checks = True
            elif current_minutes >= base_time_1 and current_minutes < base_time_3:#5分钟内
                if am_pct > 0.1:# 主力流入占比成交额大于10%
                    should_skip_further_checks = True
        
        if should_skip_further_checks:
            yfyLog.logger.info(f"[{account_type},{symbol}] 打板卖出策略2, 主净额占比：{am_pct}")
            return

        if is_down_limit:
            sell_reason = "跌停，挂单卖出。"  # 跌停则不继续执行
            yfyLog.logger.info(f"[{account_type},{symbol}] 打板卖出策略2, 跌停状态，挂单卖出。")

        # 3. 获取数据
        # 分时数据 (价格, 均价, 成交量等)
        try:
            yfydata_mPriceInfo = YFYDataMinutePriceInfo()
            price_info = yfydata_mPriceInfo.get_price_info([symbol])
            minute_price_df = price_info[price_info['symbol'] == symbol]
            min_data_points_needed = 5 # 所需最少数据点 (原为 max(10, 10 + 1, 60))
            if minute_price_df is None or minute_price_df.empty or len(minute_price_df) < min_data_points_needed:
                yfyLog.logger.warning(f"[{account_type},{symbol}] 打板卖出策略2, 分时数据不足 (需要 {min_data_points_needed}, 获取到 {len(minute_price_df) if minute_price_df is not None else 0}).")
                return

            # --- 策略判断 --- 
            # 策略A: 基于 check_mPrice_sell (覆盖用户条件1 和 4: 快速下跌、低开低走、离均线远)
            is_sell_cms, reasons_cms = sUtils.check_mPrice_sell(
                price_info=minute_price_df,
                symbol=symbol,
                sell_score_threshold=3,
                score_large_drop=3,
                score_sharp_drop=4,
                score_cont_duration=2,
                score_cont_pct=2,
                use_dynamic_drop_threshold=True,
                large_drop_pct=2.0,
                volatility_window=60,
                volatility_factor=2.0,
                roc_window=10,
                sharp_drop_roc_pct=-1.0,
                continuous_below_minutes=30,
                scan_minutes_for_pct=30.0,
                below_pct_threshold=0.7
            )
            if is_sell_cms:
                sell_reason = f"核心信号触发,{reasons_cms.get('total_score', '')} {reasons_cms}"

            # 获取最新的均价，供后续策略使用
            latest_avg_price = minute_price_df['avg_price'].iloc[-1] if not minute_price_df.empty else 0
            # 策略B: 从日内高点回落 (仅在均线上方时判断) - 如果策略A未触发
            if not sell_reason:
                # 仅当价格在均线上方时，才判断是否从高点回落
                if close > latest_avg_price > 0 and close < high * (1 - pullback_pct_from_intraday_high):
                    sell_reason = f"均线上方高点回落,现价{close:.2f}(>均价{latest_avg_price:.2f}) 从高点{high:.2f}回落超过{(pullback_pct_from_intraday_high*100):.1f}%"

            # 策略C: 5分钟均价大幅高于日均价 (乖离过大) - 如果前面策略未触发
            if not sell_reason:
                if len(minute_price_df) >= 5 and latest_avg_price > 0:
                    # 计算最近5分钟的成交价均值
                    ma5_price = minute_price_df['price'].tail(5).mean()
                    # 如果5分钟均价超过日均价5% 且 未涨停，则卖出
                    if not is_up_limit and ma5_price > (latest_avg_price * 1.05):
                        sell_reason = f"5分钟均价乖离过大,5分钟均价{ma5_price:.2f} > 日均价{latest_avg_price:.2f}的5%"
        
        except Exception as e:
            yfyLog.logger.error(f"获取股票数据出错: {e}")
            
        # 策略D: 时间窗口强制卖出逻辑
        # D.1: 14:53 强制卖出
        if now.hour == 14 and now.minute >= 53:
            if not is_up_limit:
                # 如果已有其他卖出理由，附加此时间条件；否则，以此为主要理由
                sell_reason = (f"{sell_reason}; 14-53强制卖出(未涨停)" if sell_reason else "14-53强制卖出(未涨停)")
            else:
                yfyLog.logger.info(f"[{account_type}:{symbol}] 打板卖出策略2,14-53已涨停，继续持有。")
                return # 涨停则不卖，函数结束

        # --- 执行卖出 --- 
        if sell_reason:
            yfyLog.logger.info(f"[{account_type}:{symbol}] 打板卖出策略2决策卖出。原因, {sell_reason}")
            try:
                yfyam.sell_stock(account_type=account_type, symbol=symbol, sell_pct=sell_pct, msg=f"打板卖出2, {sell_reason}")
                yfyam.get_account_datainfo(account_type=account_type) # 更新持仓信息
            except Exception as e:
                yfyLog.logger.error(f"[{account_type}:{symbol}] 打板卖出策略2执行卖出失败, {e}", exc_info=True)
            return # 无论成功与否，决策已定
            
    except Exception as e:
        yfyLog.logger.error(f"[{account_type}:{symbol}] 打板卖出策略2执行出错, {e}", exc_info=True)


def data_clear():
    pass

if __name__ == "__main__":
    db_stock_sell_strategy2(account_type='feng_2', symbol='000096')
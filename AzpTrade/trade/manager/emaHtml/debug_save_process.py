#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试保存过程，模拟前端发送数据并检查保存结果
"""

import sys
import os
import json
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from trade.strategy.StrategiesConfigRedisMgr import strategies_config_redis_mgr
from trade.redis.StockRedisMgr import StockRedisMgr
from trade.strategy.RedisKeyPrefix import RedisKeyPrefix

def main():
    strategy_name = "打120日新高涨停"
    
    print(f"🧪 调试保存过程: {strategy_name}")
    print("=" * 60)
    
    # 1. 模拟前端发送的配置数据
    configurations = [
        {
            "account_type": "A1模拟1",
            "cash_pct": 0.05,
            "limit_pct_chg": [2, 4],
            "buy_amount": 0,
            "limit_max_pct_chg": 2,
            "max_buy_cnt": 2,
            "symbol_types": "00,60,30",
            "is_simulate": True,
            "cancel_time": 60,
            "dyn_pos": True
        }
    ]
    
    print(f"📤 模拟前端配置数据:")
    print(f"   类型: {type(configurations)}")
    print(f"   长度: {len(configurations)}")
    print(f"   内容: {configurations}")
    print()
    
    # 2. 调用保存方法
    print("💾 调用保存方法...")
    try:
        success = strategies_config_redis_mgr.save_strategy_buy_data_to_redis(strategy_name, configurations)
        print(f"✅ 保存结果: {success}")
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return
    print()
    
    # 3. 直接从Redis读取原始数据
    redis_mgr = StockRedisMgr()
    key = f"{RedisKeyPrefix.STRATEGY_CONFIG.value}:{strategy_name}"
    
    print("🔍 检查保存后的Redis数据:")
    try:
        # 使用 _redis.get() 获取原始字符串
        raw_data = redis_mgr._redis.get(key)
        print(f"   原始数据类型: {type(raw_data)}")
        print(f"   原始数据内容: {raw_data[:200] if raw_data else 'None'}...")
        if raw_data and len(raw_data) > 200:
            print(f"   (原始数据长度: {len(raw_data)})")
        print()
        
        # 使用 get_value() 获取反序列化数据
        processed_data = redis_mgr.get_value(key)
        print(f"   处理后数据类型: {type(processed_data)}")
        if isinstance(processed_data, dict):
            print(f"   字典键: {list(processed_data.keys())}")
            configs = processed_data.get('configurations', [])
            print(f"   configurations类型: {type(configs)}")
            print(f"   configurations长度: {len(configs) if isinstance(configs, list) else '不是列表'}")
        else:
            print(f"   处理后数据内容: {str(processed_data)[:200]}...")
        print()
        
    except Exception as e:
        print(f"❌ 检查Redis数据失败: {e}")
        return
    
    # 4. 使用StrategiesConfigRedisMgr读取
    print("📋 使用策略配置管理器读取:")
    try:
        retrieved_config = strategies_config_redis_mgr.get_strategy_buy_config(strategy_name)
        print(f"   读取结果类型: {type(retrieved_config)}")
        if retrieved_config:
            print(f"   读取结果长度: {len(retrieved_config)}")
            print(f"   读取成功: ✅")
        else:
            print(f"   读取结果: {retrieved_config}")
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查Redis中策略数据的实际格式（调试版本）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from trade.manager.StockRedisMgr import StockRedisMgr
from trade.strategy.RedisKeyPrefix import RedisKeyPrefix

def main():
    redis_mgr = StockRedisMgr()
    
    strategy_name = "打120日新高涨停"
    key = f"{RedisKeyPrefix.STRATEGY_CONFIG.value}:{strategy_name}"
    
    print(f"检查Redis Key: {key}")
    print("=" * 50)
    
    # 使用原始Redis命令获取数据
    try:
        # 使用get()方法（原始数据）
        raw_data = redis_mgr.get(key)
        print(f"使用redis_mgr.get()获取的原始数据:")
        print(f"  数据类型: {type(raw_data)}")
        print(f"  数据内容: {raw_data}")
        print()
        
        # 使用get_value()方法（自动反序列化后）
        processed_data = redis_mgr.get_value(key)
        print(f"使用redis_mgr.get_value()获取的处理数据:")
        print(f"  数据类型: {type(processed_data)}")
        print(f"  数据内容: {processed_data}")
        print()
        
        # 如果processed_data是字符串，尝试手动解析
        if isinstance(processed_data, str):
            import json
            try:
                manually_parsed = json.loads(processed_data)
                print(f"手动JSON解析后:")
                print(f"  数据类型: {type(manually_parsed)}")
                print(f"  数据内容: {manually_parsed}")
            except Exception as e:
                print(f"手动JSON解析失败: {e}")
        
        # 检查键是否存在
        exists = redis_mgr.exists(key)
        print(f"键是否存在: {exists}")
        
    except Exception as e:
        print(f"获取数据时发生错误: {e}")

if __name__ == "__main__":
    main()

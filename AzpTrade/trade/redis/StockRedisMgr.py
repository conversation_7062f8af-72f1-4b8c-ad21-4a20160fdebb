import redis
from typing import List, Dict, Optional, Any, Union
import json
from datetime import datetime, date
import trade.log.YfyLog as yfyLog
import socket
from enum import Enum, auto
import re
import trade.network.yfy_base_data as yfyBaseData


class RedisKeyPrefix(Enum):
    """Redis键前缀枚举"""
    # 股票信息
    INFO = 'yf:stock:info'          # 基本信息
    INFO_DAILY = 'yf:stock:info:daily'    # 每日数据
    INFO_REAL = 'yf:stock:info:real'      # 实时数据
    INFO_KLINE = 'yf:stock:info:kline'    # K线数据
    INFO_SIMILARITY = 'yf:stock:info:similarity'    # 股票相似度
    INFO_ANALYSIS = 'yf:stock:info:analysis'    # 股票分析结果
    
    # 市场行情
    MARKET = 'yf:stock:market'            # 市场基本信息
    MARKET_INDEX = 'yf:stock:market:index'  # 指数行情
    MARKET_STATS = 'yf:stock:market:stats'  # 市场统计（涨跌家数等）
    MARKET_TREND = 'yf:stock:market:trend'  # 市场趋势
    MARKET_HOT = 'yf:stock:market:hot'      # 市场热点
    MARKET_LIMIT = 'yf:stock:market:limit'  # 涨跌停数据
    MARKET_FUNDS = 'yf:stock:market:funds'  # 资金流向
    MARKET_SENTIMENT = 'yf:stock:market:sentiment'  # 市场情绪
    MARKET_ANALYSIS = 'yf:stock:market:analysis'  # 市场成交额
    
    # 板块信息
    BOARD = 'yf:stock:board'              # 板块基本信息
    BOARD_STOCK = 'yf:stock:board:stock'  # 板块股票关系
    BOARD_TREND = 'yf:stock:board:trend'  # 板块走势
    
    # 概念信息
    CONCEPT = 'yf:stock:concept'           # 概念基本信息
    CONCEPT_STOCK = 'yf:stock:concept:stock' # 概念股票关系
    CONCEPT_HOT = 'yf:stock:concept:hot'    # 概念热度
    
    # 账户信息
    ACCOUNT = 'yf:stock:account'           # 账户基本信息
    ACCOUNT_TRADE = 'yf:stock:account:trade' # 交易记录
    ACCOUNT_POSITION = 'yf:stock:account:position' # 持仓信息
    ACCOUNT_CONFIG = 'yf:stock:account:config' # 账户配置
    
    # 策略信息
    STRATEGY = 'yf:stock:strategy'             # 策略信息
    STRATEGY_SYMBOLS = 'yf:stock:strategy:symbols' # 策略股票
    STRATEGY_ANALYSIS  = 'yf:stock:strategy:analysis'
    STRATEGY_SIMILARITY  = 'yf:stock:strategy:Similarity'
    STRATEGY_HORIZONTAL_BREAKOUT  = 'yf:stock:strategy:horizontalBreakout'
    STRATEGY_TOP_VOLUME = 'yf:stock:strategy:topVolume'
    STRATEGY_CONFIG = 'yf:stock:strategy:config' #策略配置
    
    # 交易信息
    TRADE = 'yf:stock:trade'                   # 交易基本信息
    TRADE_ETF = 'yf:stock:trade:etfs'                   # ETF基本信息
    TRADE_T0 = 'yf:stock:trade:etfs:t0'             # T+0交易信息
    TRADE_T0_STATUS = 'yf:stock:trade:etfs:t0:status'  # T+0交易状态
    TRADE_T0_POSITION = 'yf:stock:trade:etfs:t0:position'  # T+0交易持仓
    TRADE_T0_RECORD = 'yf:stock:trade:etfs:t0:record'  # T+0交易记录
    TRADE_ETF_CONFIG = 'yf:stock:trade:etfs:config'  # ETF配置信息
    
    TRADE_STOCK = 'yf:stock:trade:stocks'             # Stock基本信息
    TRADE_STOCK_SELL = 'yf:stock:trade:stocks:sell'  # Stock卖出信息
    TRADE_STOCK_CONFIG = 'yf:stock:trade:stocks:config'  # Stock配置信息
    

    # 实盘交易信息
    REAL_TRADE = 'yf:stock:trade:real'                   # 实盘交易基本信息
    REAL_TRADE_MONITOR_DB = 'yf:stock:trade:real:monitor'                   # 实盘交易监控打板数据库
    REAL_TRADE_STRATEGY = 'yf:stock:trade:real:strategy'  # 实盘策略交易任务

    # 网格交易信息
    GRID_TRADE = 'yf:stock:grid:trade'             # 网格交易基本信息
    GRID_TRADE_POSITION = 'yf:stock:grid:trade:position'  # 网格交易持仓
    GRID_TRADE_RECORD = 'yf:stock:grid:trade:record'  # 网格交易记录
    GRID_TRADE_CONFIG = 'yf:stock:grid:trade:config'  # 网格交易配置
    GRID_TRADE_STATUS = 'yf:stock:grid:trade:status'  # 网格交易状态
    GRID_TRADE_SIGNAL = 'yf:stock:grid:trade:signal'  # 网格交易信号

    # 工具
    TOOLS_PATTERN = 'yf:stock:tools:pattern'   # 选股模式
    TOOLS_FILTER = 'yf:stock:tools:filter'     # 过滤条件
    TOOLS_MONITOR = 'yf:stock:tools:monitor'   # 监控配置
    
    # 其它
    OTHERS_LOG = 'yf:stock:others:log'         # 日志信息
    OTHERS_CACHE = 'yf:stock:others:cache'     # 缓存数据
    OTHERS_TEMP = 'yf:stock:others:temp'       # 临时数据


class StockRedisMgr:
    """股票数据Redis管理器"""
    _instance = None
    _pool = None

    @staticmethod
    def build_key(*args: str) -> str:
        """构建Redis键
        Example:
            build_key(RedisKeyPrefix.INFO.value, '000001') -> 'yf:stock:info:000001'
            build_key(RedisKeyPrefix.BOARD.value, 'industry') -> 'yf:stock:board:industry'
        """
        # 过滤掉空值
        valid_parts = [str(arg).strip() for arg in args if arg]
        # 使用冒号连接各个部分
        return ':'.join(valid_parts)

    @classmethod
    def reset_instance(cls):
        """重置实例，强制重新创建连接池"""
        yfyLog.logger.info("重置Redis管理器实例")
        if cls._pool:
            try:
                cls._pool.disconnect()
            except Exception as e:
                yfyLog.logger.warning(f"断开旧连接池时出错: {e}")
        cls._instance = None
        cls._pool = None

    def __new__(cls, force_new=False):
        if cls._instance is None or force_new:
            if force_new:
                cls.reset_instance()
            
            cls._instance = super().__new__(cls)
            # 判断是否为内网环境
            host = '127.0.0.1' if yfyBaseData.is_intranet else yfyBaseData.w_ip
            yfyLog.logger.info(f"Redis连接地址: {host}")
            
            # 创建连接池（添加连接保活和超时配置）
            cls._pool = redis.ConnectionPool(
                host=host,
                port=6379,
                db=0,
                password='kGMCIy7HXE1I21ng',
                decode_responses=True,  # 自动解码响应
                socket_timeout=10.0,    # 套接字操作超时
                socket_connect_timeout=5.0,  # 连接超时
                socket_keepalive=True,  # 保持连接活跃
                socket_keepalive_options={},  # TCP keepalive选项
                health_check_interval=30,    # 健康检查间隔(秒)
                retry_on_timeout=True,  # 超时时重试
                max_connections=50,     # 连接池最大连接数
            )
        return cls._instance

    def __init__(self, force_new=False):
        if not hasattr(self, '_redis') or force_new:
            self._redis = redis.Redis(connection_pool=self._pool)

    def _serialize(self, value: Any) -> str:
        """序列化数据"""
        if isinstance(value, (date, datetime)):
            return value.isoformat()
        return json.dumps(value, ensure_ascii=False)

    def _deserialize(self, value: str) -> Any:
        """反序列化数据"""
        try:
            return json.loads(value)
        except json.JSONDecodeError:
            return value

    def set_value(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置单个键值对
        Args:
            key: Redis键
            value: 要存储的值
            expire: 过期时间（秒）
        Returns:
            bool: 是否成功
        """
        try:
            serialized = self._serialize(value)
            if expire:
                return self._redis.setex(key, expire, serialized)
            return self._redis.set(key, serialized)
        except Exception as e:
            yfyLog.logger.error(f"Redis设置值失败: {e}")
            return False

    def get_value(self, key: str) -> Optional[Any]:
        """获取单个键的值"""
        try:
            value = self._redis.get(key)
            return self._deserialize(value) if value else None
        except Exception as e:
            yfyLog.logger.error(f"Redis获取值失败: {e}")
            return None

    def batch_set(self, data: Dict[str, Any], expire: Optional[int] = None) -> bool:
        """批量设置键值对
        Args:
            data: 键值对字典
            expire: 过期时间（秒）
        Returns:
            bool: 是否全部成功
        """
        try:
            with self._redis.pipeline() as pipe:
                for key, value in data.items():
                    serialized = self._serialize(value)
                    if expire:
                        pipe.setex(key, expire, serialized)
                    else:
                        pipe.set(key, serialized)
                results = pipe.execute()
                return all(results)
        except Exception as e:
            yfyLog.logger.error(f"Redis批量设置值失败: {e}")
            return False

    def batch_get(self, keys: List[str]) -> Dict[str, Any]:
        """批量获取键值对
        Args:
            keys: 键列表
        Returns:
            Dict[str, Any]: 键值对字典
        """
        try:
            with self._redis.pipeline() as pipe:
                for key in keys:
                    pipe.get(key)
                values = pipe.execute()
                return {
                    key: self._deserialize(value) if value else None
                    for key, value in zip(keys, values)
                }
        except Exception as e:
            yfyLog.logger.error(f"Redis批量获取值失败: {e}")
            return {}

    def delete_keys(self, keys: Union[str, List[str]]) -> int:
        """删除一个或多个键
        Args:
            keys: 单个键或键列表
        Returns:
            int: 成功删除的键数量
        """
        try:
            if isinstance(keys, str):
                return self._redis.delete(keys)
            return self._redis.delete(*keys)
        except Exception as e:
            yfyLog.logger.error(f"Redis删除键失败: {e}")
            return 0

    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            return bool(self._redis.exists(key))
        except Exception as e:
            yfyLog.logger.error(f"Redis检查键存在失败: {e}")
            return False

    def clear_db(self) -> bool:
        """清空当前数据库"""
        try:
            return self._redis.flushdb()
        except Exception as e:
            yfyLog.logger.error(f"Redis清空数据库失败: {e}")
            return False

    def keys(self, pattern: str):
        """获取匹配的所有键"""
        try:
            return self._redis.keys(pattern)
        except Exception as e:
            yfyLog.logger.error(f"Redis获取keys失败: {e}")
            return []

    # -------------------- 哈希封装 --------------------
    def hset_value(self, key: str, field: str, value: Any, expire: int | None = None) -> bool:
        """在哈希键 `key` 的 `field` 位置写入 value。

        Args:
            key: Redis 哈希键
            field: 字段名
            value: 要保存的数据，任意 Python 对象
            expire: 可选，整型秒数，若传入则在写入后设置过期时间
        """
        try:
            serialized = self._serialize(value)
            pipe = self._redis.pipeline()
            pipe.hset(key, field, serialized)
            if expire:
                pipe.expire(key, expire)
            pipe.execute()
            return True
        except Exception as e:
            yfyLog.logger.error(f"Redis HSET 失败: {e}")
            return False

    def hget_value(self, key: str, field: str) -> Any | None:
        """获取哈希键中指定字段的值，自动反序列化"""
        try:
            value = self._redis.hget(key, field)
            return self._deserialize(value) if value else None
        except Exception as e:
            yfyLog.logger.error(f"Redis HGET 失败: {e}")
            return None

    def hgetall_value(self, key: str) -> dict[str, Any]:
        """获取哈希键所有字段，返回反序列化后的 dict"""
        try:
            raw_dict = self._redis.hgetall(key)
            return {k: self._deserialize(v) for k, v in raw_dict.items()}
        except Exception as e:
            yfyLog.logger.error(f"Redis HGETALL 失败: {e}")
            return {}


'''
redis_mgr = StockRedisMgr()

# 示例1：存储和获取股票基本信息
key = redis_mgr.build_key(RedisKeyPrefix.INFO.value, '000001')  # 生成 'yf:stock:info:000001'
redis_mgr.set_value(key, {"name": "平安银行", "price": 10.5})
stock_info = redis_mgr.get_value(key)  # 返回: {"name": "平安银行", "price": 10.5}

# 示例2：存储和获取板块股票关系
key = redis_mgr.build_key(RedisKeyPrefix.BOARD_STOCK.value, 'industry_001')
redis_mgr.set_value(key, ["000001", "000002", "000003"])
board_stocks = redis_mgr.get_value(key)  # 返回: ["000001", "000002", "000003"]

# 示例3：批量获取多个股票信息
keys = [
    redis_mgr.build_key(RedisKeyPrefix.INFO.value, '000001'),
    redis_mgr.build_key(RedisKeyPrefix.INFO.value, '000002')
]
stocks_info = redis_mgr.batch_get(keys)  # 返回: {"yf:stock:info:000001": {...}, "yf:stock:info:000002": {...}}

# 示例4：存储和获取带过期时间的数据（例如缓存）
cache_key = redis_mgr.build_key(RedisKeyPrefix.OTHERS_CACHE.value, 'daily_stats')
redis_mgr.set_value(cache_key, {"total": 100, "up": 60}, expire=3600)  # 1小时后过期
cache_data = redis_mgr.get_value(cache_key)  # 如果未过期，返回: {"total": 100, "up": 60}

# 示例5：检查键是否存在
pattern_key = redis_mgr.build_key(RedisKeyPrefix.TOOLS_PATTERN.value, 'pattern_001')
if redis_mgr.exists(pattern_key):
    pattern = redis_mgr.get_value(pattern_key)
else:
    pattern = None  # 键不存在

# 示例6：删除数据
position_key = redis_mgr.build_key(RedisKeyPrefix.ACCOUNT_POSITION.value, 'user_001')
redis_mgr.delete_keys(position_key)  # 删除单个键
redis_mgr.delete_keys([position_key, cache_key])  # 删除多个键

# 示例7：存储和获取市场行情数据
# 7.1 存储指数行情
index_key = redis_mgr.build_key(RedisKeyPrefix.MARKET_INDEX.value, 'sh000001')
redis_mgr.set_value(index_key, {
    "name": "上证指数",
    "price": 3012.65,
    "change": 0.85,
    "change_pct": 0.28,
    "timestamp": "2024-12-17 10:53:55"
})

# 7.2 存储市场统计数据
stats_key = redis_mgr.build_key(RedisKeyPrefix.MARKET_STATS.value, '********')
redis_mgr.set_value(stats_key, {
    "up_count": 2356,
    "down_count": 1845,
    "limit_up": 85,
    "limit_down": 12,
    "new_high": 45,
    "new_low": 8
})

# 7.3 存储市场资金流向
funds_key = redis_mgr.build_key(RedisKeyPrefix.MARKET_FUNDS.value, '********')
redis_mgr.set_value(funds_key, {
    "main_in": 2156.78,  # 主力净流入（亿）
    "retail_in": -568.45,  # 散户净流入（亿）
    "north_bound": 12.56,  # 北向资金（亿）
    "industry_funds": {
        "医药": 12.45,
        "科技": -5.67,
        "金融": 8.89
    }
})

# 7.4 批量获取市场数据
market_keys = [
    redis_mgr.build_key(RedisKeyPrefix.MARKET_INDEX.value, 'sh000001'),
    redis_mgr.build_key(RedisKeyPrefix.MARKET_STATS.value, '********'),
    redis_mgr.build_key(RedisKeyPrefix.MARKET_FUNDS.value, '********')
]
market_data = redis_mgr.batch_get(market_keys)

# 7.5 存储市场情绪指标（带过期时间）
sentiment_key = redis_mgr.build_key(RedisKeyPrefix.MARKET_SENTIMENT.value, 'fear_greed')
redis_mgr.set_value(sentiment_key, {
    "index": 65,  # 恐慌贪婪指数
    "level": "贪婪",
    "factors": {
        "volatility": 70,
        "momentum": 65,
        "volume": 60
    }
}, expire=3600)  # 1小时后过期

'''
import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

import trade.network.YFYDataMgr as yfydm
import trade.log.YfyLog as yfylog
import trade.network.YFYAccountMgr as yfyam
import os
import json
from trade.manager.StockQualityScoreMgr import StockQualityScoreMgr
from trade.redis.StockRedisMgr import StockRedisMgr, RedisKeyPrefix

# <-----------------------------策略数据------------------------------------->
BUY_SINGAL_STRATEGY = "买信号"
EMA_BUY_SINGAL_STRATEGY = "趋势票买信号"
HYZL_BUY_SINGAL_STRATEGY = "HYZL买信号"
BUY_SINGAL_STRATEGY_3 = "买信号3"
BUY_SINGAL_SCORE_STRATEGY = "买信号分买"
FIVEMIN_SINGAL_STRATEGY = "5分钟K"
QUSHI_STRATEGY = "趋势"
PAIMING_STRATEGY = "排名股池"
RANKRATIO_STRATEGY = "排名变化"
TOPPOS_STRATEGY = "Top前排票"
TOPZHUJING_STRATEGY = "Top主净"
ZHUJING_STRATEGY = "主净"
RANKONE_STRATEGY = "排名第一"
XIAOSHIZHI_STRATEGY = "小市值"
XIAOGAOPOOL_60_STRATEGY = "新高股池60日"
XIAOGAOPOOL_120_STRATEGY = "新高股池120日"
XIAOGAOPOOL_250_STRATEGY = "新高股池250日"
BREAKEMA_STRATEGY = "均线突破"
RANKONETEST_STRATEGY = "排名第一Test"
FIRST10ZTGC_STRATEGY = "打10cmGC首板"
FIRST10ZTGC_T_STRATEGY = "打10cmGC首板Test"
FIRST10ZTGC_T200_STRATEGY = "打10cmGC前200首板"
FIRST10ZT_ALL_STRATEGY = "打10cm全场首板"
XIAOGAO_120ZT_STRATEGY = "打120日新高涨停"
FIRST10ZTGC2B_STRATEGY = "打10cmGC二板"
FIRST20ZTGC_STRATEGY = "打20cmGC首板"
FIRST10ZT_STRATEGY = "打10cm首板"
FIRST10ZT2B_STRATEGY = "打10cm二板"
FIRST10ZT5B_STRATEGY = "打10cm五板"
ENV10ZT_STRATEGY = "打Env10cm涨停"
FIRST20ZT_STRATEGY = "打20cm首板"
FIRST10ZT_TEST_STRATEGY = "打10cm首板Test"
FIRST10ZTGC_TEST_STRATEGY = "打10cmGC首板T"
BREAKRESIST_FT10ZT_STRATEGY = "破压力位10cm首板"
NEWSINFOTEST_STRATEGY = "新闻资讯"
TOP100_STRATEGY = "Top100排名"
KPLTCK_STRATEGY = "kpl题材库"
THXREDU_STRATEGY = "thx热度榜"
ZT3D_STRATEGY = "3日涨停"
LATE_SESSION_STRATEGY = "尾盘多头"
SHORT_LATE_SESSION_STRATEGY = "短线尾盘"
ZHUJINGRATIO_STRATEGY = "主净排名"
TOPPAIMING_STRATEGY = "Top排名"
LIANBAN10ZT_STRATEGY = "打10cm连板"
LIANBAN20ZT_STRATEGY = "打20cm连板"
T0ETF_STRATEGY = "T0ETF"
STAND_SUPPORT_STRATEGY = "站稳支撑位"
MULTITOPRANK_STRATEGY = "多排名前排"
MULTITOPRANK5MIN_STRATEGY = "多排名5Min前排"
TOPCONCEPT_STRATEGY = "概念前排"

MULTI_TRIGGER_STRATEGY = "多策略共振"
# -----------------------------------------------#
USER_SOFTWARE_BUY = "交易软件买"
EMA_AUTO_BUY = "均线自动"
PCT_AUTO_BUY = "涨跌自动"


def get_buy_data(account_type, cash_pct=0.05, limit_pct_chg=None, buy_amount=0, limit_max_pct_chg=2, max_buy_cnt=2,
                 symbol_types="00,60,30", is_simulate=True, cancel_time=60,is_dyn_pos=True):
    if limit_pct_chg is None:
        limit_pct_chg = [2, 4]
    buy_data = {
        "account_type": account_type,
        "cash_pct": cash_pct,
        "limit_pct_chg": limit_pct_chg,
        "buy_amount": buy_amount,
        "limit_max_pct_chg": limit_max_pct_chg,
        "max_buy_cnt": max_buy_cnt,
        "symbol_types": symbol_types,
        "dyn_pos":is_dyn_pos,  # 动态仓位比
        "is_simulate": is_simulate,
        "cancel_time": cancel_time
    }
    return buy_data


def get_strategy_buy_data(strategy_type: str = "", symbol: str = "", use_redis: bool = True):
    """
    获取策略买入配置数据，从Redis获取配置
    
    Args:
        strategy_type: 策略类型
        symbol: 股票代码（可选）
        use_redis: 是否使用Redis配置，默认True
        
    Returns:
        list: 买入配置数据列表
    """
    
    # 从Redis获取策略配置
    if use_redis:
        try:
            from trade.strategy.StrategiesConfigRedisMgr import get_enhanced_strategy_buy_data_from_redis
            redis_config = get_enhanced_strategy_buy_data_from_redis(strategy_type, symbol)
            if redis_config:
                yfylog.logger.info(f"从Redis获取策略配置: {strategy_type}, 配置数量: {len(redis_config)}")
                # 应用动态调整逻辑
                adjusted_config = apply_dynamic_adjustments(redis_config, symbol)
                return adjusted_config
            else:
                yfylog.logger.warning(f"Redis中不存在策略配置: {strategy_type}")
                return []
        except Exception as e:
            yfylog.logger.error(f"从Redis获取策略配置异常: {strategy_type}, 错误: {e}")
            return []
    
    yfylog.logger.warning(f"未启用Redis配置获取: {strategy_type}")
    return []
    


def apply_dynamic_adjustments(buy_data_list: list, symbol: str = "") -> list:
    """
    应用动态调整逻辑到买入配置数据
    
    Args:
        buy_data_list: 从 Redis 获取的原始买入配置列表
        symbol: 股票代码（可选）
        
    Returns:
        list: 动态调整后的买入配置列表
    """
    
    if not buy_data_list:
        return []
    
    # 1. 根据 symbol 过滤适合的股票类型
    filtered_data = []
    for data in buy_data_list:
        if symbol:
            symbol_types_list = data.get("symbol_types", "00,60,30,68").split(",")
            if symbol[:2] in symbol_types_list:
                filtered_data.append(data)
        else:
            filtered_data.append(data)
    
    buy_data_list = filtered_data if filtered_data else buy_data_list
    
    # 2. 获取市场数据用于动态调整
    limit_pct_chg_ratio = 0
    limit_3dPct_chg_ratio = 1
    
    if symbol:
        try:
            import trade.network.YFYDataMgr as yfydm
            symbol_simple_info = yfydm.get_stock_simple_info(symbol)
            if symbol_simple_info is not None and not symbol_simple_info.empty:
                yesterdayRank = symbol_simple_info['yesterdayRank'].values[0]
                rank = symbol_simple_info['rank'].values[0]
                if yesterdayRank > 300:
                    limit_pct_chg_ratio = max((yesterdayRank - rank) / 500, yesterdayRank / rank)
                else:
                    limit_pct_chg_ratio = max((yesterdayRank - rank) / 50, yesterdayRank / rank)
                
                limit_3dPct_chg_ratio = min(1, 1 - symbol_simple_info["changePercent3D"].values[0] / 100)
        except Exception as e:
            yfylog.logger.warning(f"获取股票 {symbol} 基础信息失败: {e}")
    
    # 3. 获取市场整体情况
    try:
        import trade.network.YFYDataMgr as yfydm
        all_stock_df = yfydm.all_stock_info_df
        all_count = len(all_stock_df)
        count_greater_than_zero = all_stock_df["changePercent"].gt(0).sum()
        count_equal_to_zero = all_stock_df["changePercent"].eq(0).sum()
        rise_pct = float((count_greater_than_zero + count_equal_to_zero) / all_count)
        rise_pct = max(rise_pct, 0.65)
    except Exception as e:
        yfylog.logger.warning(f"获取市场整体数据失败: {e}")
        rise_pct = 0.65
    
    # 4. 获取股票质量评分信息
    calculate_position = 1.0
    if symbol:
        try:
            from trade.manager.StockQualityScoreMgr import StockQualityScoreMgr
            score_mgr = StockQualityScoreMgr()
            total_score, details = score_mgr.get_stock_quality_score(symbol)
            calculate_position = score_mgr.calculate_position(total_score)
            yfylog.logger.info(f"股票{symbol}动态仓位计算 - 质量分: {total_score:.2f}, 仓位比例: {calculate_position:.3f}")
        except Exception as e:
            yfylog.logger.warning(f"获取股票 {symbol} 质量评分失败: {e}")
    
    # 5. 应用动态调整
    for data in buy_data_list:
        # 调整涨停限制比例
        if limit_pct_chg_ratio > 1:
            limit_pct_10cm = (data["limit_pct_chg"][0] + min(limit_pct_chg_ratio * 0.8, 
                                                             data.get("limit_max_pct_chg", 5))) * rise_pct
            limit_pct_20cm = (data["limit_pct_chg"][1] + min(limit_pct_chg_ratio * 0.8,
                                                             data.get("limit_max_pct_chg", 5))) * rise_pct
            data["limit_pct_chg"] = [limit_pct_10cm, limit_pct_20cm]
        
        # 调整买入金额（根据连板数）
        buy_amount = data.get("buy_amount", 0)
        if buy_amount > 0 and symbol:
            try:
                import trade.network.YFYDataMgr as yfydm
                symbol_simple_info = yfydm.get_stock_simple_info(symbol)
                if symbol_simple_info is not None and not symbol_simple_info.empty:
                    lianbanCount = symbol_simple_info["lianbanCount"].values[0]
                    buy_amount_pct = 1
                    if lianbanCount > 2:
                        buy_amount_pct = min(lianbanCount / 2, 4)
                    data["buy_amount"] = buy_amount / buy_amount_pct
            except Exception as e:
                yfylog.logger.warning(f"调整连板数失败: {e}")
        
        # 应用动态仓位调整（暂时禁用，与原版本保持一致）
        # if data.get("dyn_pos", False):
        #     data["buy_amount"] = data.get("buy_amount", 0) * calculate_position
        #     data["cash_pct"] = data.get("cash_pct", 0.1) * calculate_position
    
    return buy_data_list


# ---------------------#---------------------#---------------------#
last_modified_time = None
strategy_config = {}
current_file_path = os.path.abspath(__file__)
directory_path = os.path.dirname(current_file_path)
url = directory_path + "/strategy_config.json"


def get_strategy_config():
    global strategy_config, url
    p_json = json.loads(open(url, encoding="utf-8").read())
    strategy_config = p_json


def check_reload_json():
    # current_file_path = os.path.abspath(__file__)
    # directory_path = os.path.dirname(current_file_path)
    # print(current_file_path, directory_path)

    global last_modified_time, url
    # 获取文件的最后修改时间
    current_modified_time = os.path.getmtime(url)
    # 如果文件的修改时间与上次记录的不同，则重启程序
    if last_modified_time is None or current_modified_time != last_modified_time:
        last_modified_time = current_modified_time
        get_strategy_config()
        yfylog.logger.warning(f"{url},配置文件已更新😄")
    else:
        yfylog.logger.info("配置文件strategy_config.json没有改变")


def get_user_control_symbols():
    global strategy_config
    if strategy_config is None or "user_control_symbols" not in strategy_config:
        return []
    # print(strategy_config["user_control_symbols"])
    return strategy_config["user_control_symbols"]


def get_symbols_sell_strategy(symbol):
    global strategy_config
    if strategy_config is None or "symbols_sell_strategy" not in strategy_config:
        return ""
    if symbol not in strategy_config["symbols_sell_strategy"]:
        return ""
    # print(strategy_config["user_control_symbols"])
    return strategy_config["symbols_sell_strategy"][symbol]


def get_account_status(account_name):  # open，close
    """
    获取账户状态，优先从Redis缓存读取，如果未命中则从配置文件加载并存入缓存。
    """
    try:
        # 1. 优先从Redis获取
        redis_mgr = StockRedisMgr()
        redis_key = redis_mgr.build_key(RedisKeyPrefix.ACCOUNT_CONFIG.value,"status")
        
        status = redis_mgr.hget_value(redis_key, account_name)
        if status is not None:
            yfylog.logger.debug(f"从Redis缓存获取账户状态: {account_name} -> {status}")
            return status
    except Exception as e:
        yfylog.logger.error(f"redis获取账户 {account_name} 状态时发生异常: {e}")
        
    # 异常情况下，降级直接从配置文件读取
    global strategy_config
    if strategy_config is not None and "account_status" in strategy_config:
        if account_name in strategy_config["account_status"]:
            return strategy_config["account_status"][account_name]
    return ""

def get_position():
    try:
        # 1. 优先从Redis获取
        redis_mgr = StockRedisMgr()
        redis_key = redis_mgr.build_key(RedisKeyPrefix.ACCOUNT_CONFIG.value,"position")
        
        position = redis_mgr.get_value(redis_key)
        if position is not None:
            yfylog.logger.debug(f"从Redis缓存获取账户仓位: {position}")
            return float(position)
    except Exception as e:
        yfylog.logger.error(f"redis获取账户仓位时发生异常: {e}")

    global strategy_config
    if strategy_config is not None and "position" in strategy_config:
        return strategy_config["position"]
    return 0.85

def get_jggc_stocks() -> list[str]:
    global strategy_config
    if strategy_config is None or "jggcjx_stocks" not in strategy_config:
        return []
    return strategy_config["jggcjx_stocks"]

check_reload_json()

#get_account_status("bing")

#s = get_position()
#print(s)
#buy_data_list = get_strategy_buy_data(strategy_type=BUY_SINGAL_STRATEGY, symbol="000988")
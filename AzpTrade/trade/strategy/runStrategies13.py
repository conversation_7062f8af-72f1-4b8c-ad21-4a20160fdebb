import time
import datetime

from trade.strategy.buys import TopBuySingalBuy as topBuySingalBuy

import trade.network.YFYDataMgr as yfydm
import trade.log.YfyLog as yfyLog
import trade.manager.TimeDateMgr as tdm
import trade.manager.StrategiesMgr as smgr
import traceback

from trade.enum.WxMsgType import WxMsgType

strategies_list = [
    topBuySingalBuy
    
]

count = 0


def update():
    global symbol_list, count, strategies_list
    # 设置一个循环，无限次执行，直到您决定停止它
    count += 1
    now = datetime.datetime.now()
    now_str = now.strftime("%Y%m%d %H:%M:%S")
    year, month, day = now.year, now.month, now.day

    is_word_day, is_opening, is_Bidding = tdm.is_open_time()
    is_bidding_end = tdm.is_end_bidding()
    # 检查是否为工作日（周一到周五）
    if is_opening or is_bidding_end:
        try:
            for i in range(len(strategies_list)):
                cls_obj = strategies_list[i]
                yfyLog.logger.info("<<<<<<<<<----------------runStrategies12121212126: %s--------------->>>>>>>>>",
                                   cls_obj.__name__)
                if cls_obj.get_fit_time():
                    symbol, name, chg_pct = cls_obj.run()
                    if symbol != "0":
                        is_has_symbol = smgr.is_symbol_inStrategy(symbol, cls_obj.STRATEGY_TYPE)
                        if not is_has_symbol:
                            stock_simple_info = yfydm.get_stock_simple_info(symbol)
                            # changPercent = stock_simple_info["changePercent"].values[0]
                            conceptSpecialName = stock_simple_info["conceptSpecialName"].values[0]
                            boardName = stock_simple_info["boardName"].values[0]
                            boardInFlowRank = stock_simple_info["boardInFlowRank"].values[0]
                            conceptInFlowRank = stock_simple_info["conceptInFlowRank"].values[0]
                            dc_mainInFlowRank = stock_simple_info["mainInFlowRank"].values[0]
                            xl_mainInFlowRank = stock_simple_info["xl_mainInFlowRank"].values[0]
                            kpl_mainInFlowRank = stock_simple_info["kpl_mainInFlowRank"].values[0]
                            amount_rank = stock_simple_info["amount_rank"].values[0]
                            mainInFlow_amount_pct = round(stock_simple_info["mainInFlowAmount"].values[0] /
                                                          stock_simple_info["volumeAmount"].values[0], 2)
                            env_score = 0
                            if "envScore" in stock_simple_info.columns:
                                env_score = stock_simple_info["envScore"].values[0]
                            yfyLog.logger.info("去购买[%s]策略的股票：%s,%s,%s", cls_obj.__name__, symbol, name, chg_pct)
                            msg = (
                                f"{now_str}【{cls_obj.STRATEGY_TYPE}】策略，股票：【{name}】-ID【{symbol}】涨跌幅：{str(chg_pct)}，"
                                f"\n概念：【{conceptSpecialName}({conceptInFlowRank})】,板块：【{boardName}({boardInFlowRank})】"
                                f"mainInFlowRank:({dc_mainInFlowRank}-{xl_mainInFlowRank}-{kpl_mainInFlowRank})"
                                f"amountRank:({amount_rank}--envScore:{env_score}--jzb:{mainInFlow_amount_pct})")
                            smgr.add_strategy_symbol(symbol, cls_obj.STRATEGY_TYPE)
                            has_buy = cls_obj.buy(symbol, name, chg_pct)
                            if has_buy:
                                smgr.push_wx_msg(symbol=symbol, strategy_type=cls_obj.STRATEGY_TYPE,
                                                 msg_type=WxMsgType.TOPRANK_STRATEGY, msg=msg)
                                msg += "-【计算买入成功】"

                            yfydm.set_log(symbol, cls_obj.STRATEGY_TYPE, msg)  # 记录日志，但是不推送微信信息

            yfyLog.logger.info(
                "<+++++++--------------------------------指标1212121212计算完成！--------------------------------+++++++>")


        except Exception as e:
            yfydm.push_wx_msg(WxMsgType.ERROR,
                              "---" + str(datetime.datetime.now()) + "---【" + traceback.format_exc() + "】---" + str(
                                  e))
            yfyLog.logger.error("发生错误：%s", traceback.format_exc() + str(e))

    else:
        if now.hour > 15:
            symbol_list = []
            symbol_list.clear()
            yfydm.data_clear()  # 释放内存，清空缓存
            smgr.data_clear()


def run1():
    while True:
        update()
        # print(is_buy_time("16:00:00", "16:01:00", "16:02:00", "16:03:00"),time.time())
        # smgr.sell_stock(smgr.SELL_60MIN_SINGAL)
        time.sleep(5)

# run1()

# for cls_obj in strategies_list:
#    yfyLog.logger.info("<<<<<<<<<--------runStrategies: %s------->>>>>>>>>", cls_obj.__name__)
#    symbol, name, chg_pct = cls_obj.run()

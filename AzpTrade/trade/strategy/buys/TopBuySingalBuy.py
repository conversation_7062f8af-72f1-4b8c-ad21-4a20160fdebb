import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

import trade.network.YFYDataMgr as yfydm
import trade.log.YfyLog as yfyLog
import pandas as pd

import datetime
import trade.manager.StrategiesMgr as smgr
import trade.strategy.StrategiesConfig as sconfig
import trade.network.YFYAccountMgr as yfyam
import trade.strategy.ZhiBiaoUtils as zbUtils
import trade.strategy.StrategiesUtils as sutils
import trade.manager.TimeDateMgr as tdm
from trade.enum.WxMsgType import WxMsgType

STRATEGY_TYPE = sconfig.TOP_BUY_SINGAL_STRATEGY

zb_sell_dict = {
    101: "cci卖",
    105: "rsi卖",
    103: "tdx卖",
    102: "龙卖",
    104: "减仓卖",
    108: "逃顶卖",
    107: "趋势背离卖",
    106: "冠顶卖"
}

zb_buy_dict = {
    1: "出击",
    #2: "cci买",
    #3: "rsi买",
    4: "牛",
    5: "擒龙",
    6: "大小底",
    7: "tdx买",
    8: "放量",
    9: "龙买",
    10: "波段",
    11: "追涨",
    12: "冠顶",
    13: "大资金",
    14: "低吸",
    15: "游资扫货",
    20: "量价齐升"
}

def create_paiming_stock():
    cmd_n1 = "yfy#股票#板块涨跌#1-3:3日:5%#板块={板块}#号码=-68-ST#跌停数=3<1#分钟3额>20万#3日换手>2%#3日涨跌>6%#运算=当前价>5日均价#运算=当前价>10日均价#排名<100#卖信号=3<1"
    sub_cmd_n1 = "yfy#板块#涨跌#1-50:3日#3日涨跌>0%"
    cmd_df1 = yfydm.get_bangai_stock(cmd_n1, sub_cmd_n1)

    cmd_n2 = "yfy#股票#板块涨跌#1-5:3日:15%#板块={板块}#号码=-68-ST#跌停数=3<1#分钟3额>20万#3日换手>2%#3日涨跌>6%#运算=当前价>5日均价#运算=当前价>10日均价#排名<150#卖信号=3<1"
    sub_cmd_n2 = "yfy#板块#涨跌#1-6:3日#3日涨跌>0%"
    cmd_df2 = yfydm.get_bangai_stock(cmd_n2, sub_cmd_n2)

    cmd_n3 = "yfy#股票#概念涨跌#1-3:3日:10%#概念={板块}#号码=-68-ST#跌停数=3<1#分钟3额>20万#3日换手>2%#3日涨跌>6%#运算=当前价>5日均价#运算=当前价>10日均价#排名<150#卖信号=3<1"
    sub_cmd_n3 = "yfy#概念#涨跌#1-15:3日#3日涨跌>0%"
    cmd_df3 = yfydm.get_bangai_stock(cmd_n3, sub_cmd_n3)

    cmd_n4 = "yfy#股票#排名#1-200#号码=-68-ST#跌停数=3<1#分钟3额>20万#3日换手>2%#3日涨跌>6%#运算=当前价>5日均价#运算=当前价>10日均价#买信号=10>1#卖信号=3<1"
    des_data4 = {
        "subscribeCmdName": cmd_n4
    }
    cmd_df4 = yfydm.get_data(yfydm.STOCK_LIST, des_data4)

    cmd_n5 = "yfy#股票#板块涨跌#1-3:0:5%#板块={板块}#号码=-68-ST#跌停数=3<1#分钟3额>20万#3日换手>2%#3日涨跌>3%#运算=当前价>5日均价#运算=当前价>10日均价#卖信号=3<1"
    sub_cmd_n5 = "yfy#板块#涨跌#1-10:0#涨跌幅>0%#主净>0"
    cmd_df5 = yfydm.get_bangai_stock(cmd_n5, sub_cmd_n5)

    cmd_n6 = "yfy#股票#板块涨跌#1-3:0:3%#板块={板块}#号码=-68-ST#跌停数=3<1#分钟3额>20万#3日换手>2%#3日涨跌>3%#运算=当前价>5日均价#运算=当前价>10日均价#卖信号=3<1"
    sub_cmd_n6 = "yfy#板块#涨跌#1-50:0"
    cmd_df6 = yfydm.get_bangai_stock(cmd_n6, sub_cmd_n6)

    cmd_n7 = "yfy#股票#板块涨跌#1-10:0:3%#板块=ALL#号码=-68-ST#涨跌幅>6%#分钟3额>20万#3日换手>2%#3日涨跌>3%"
    des_data7 = {
        "subscribeCmdName": cmd_n7
    }
    cmd_df7 = yfydm.get_data(yfydm.STOCK_LIST, des_data7)

    cmd_n8 = "yfy#股票#排名#1-5300#号码=-68-ST#分钟3额>20万#3日换手>2%#3日涨跌>3%#涨停=1"
    des_data8 = {
        "subscribeCmdName": cmd_n8
    }
    cmd_df8 = yfydm.get_data(yfydm.STOCK_LIST, des_data8)

    cmd_n9 = "yfy#股票#涨跌幅#1-50:5日#号码=-68-ST#分钟3额>20万#3日换手>2%"
    des_data9 = {
        "subscribeCmdName": cmd_n9
    }
    cmd_df9 = yfydm.get_data(yfydm.STOCK_LIST, des_data9)

    cmd_df = pd.concat([cmd_df1, cmd_df2, cmd_df3, cmd_df4, cmd_df5, cmd_df6, cmd_df7, cmd_df8, cmd_df9])
    if cmd_df.empty:
        return []

    stock_df = cmd_df.drop_duplicates(subset='symbol', keep='first', inplace=False)
    stock_df = stock_df.sort_values(by="boardName")
    symbols_str = stock_df["symbol"].astype(str).str.cat(sep=', ')
    symbols_list = stock_df["symbol"].tolist()
    
    return symbols_list

def run(is_debug: str = None):
    stock_list = create_paiming_stock()
    all_df = yfydm.all_stock_info_df
    all_s_df = all_df[all_df["symbol"].isin(stock_list)]
    all_stock_df = all_s_df.copy()

    print(f"all_stock_df len: {len(all_stock_df)}")
    print(all_stock_df["symbol"].tolist())
    #table_log = yfyLog.get_df_table_log(all_stock_df, table_name=STRATEGY_TYPE)
    #yfyLog.logger.info(table_log)
    
    if not all_stock_df.empty:
        for index, row in all_stock_df.iterrows():
            stock_df = all_stock_df[all_stock_df["symbol"] == row["symbol"]]
            
            is_curt_singal = False
            for signal in row["buy_signal_list"]:
                if "type" in signal and signal["type"] in zb_buy_dict:
                    dayCount = signal["dayCount"]
                    if dayCount < 2:
                        is_curt_singal = True

            if not is_curt_singal:
                continue    

            symbol = row["symbol"]
            name = row["name"]
            buy_signal_list = row["buy_signal_list"]
            if smgr.is_symbol_inStrategy(symbol, STRATEGY_TYPE):
                continue

            amount_rank = stock_df["amount_rank"].values[0]
            #print(symbol, amount_rank)
            if amount_rank > 300:
                continue

            is_hot_concept = sutils.check_stock_is_hot_concept(symbol=symbol,concept_count=3,recent_days=3)
            #print("is_hot_concept",symbol, is_hot_concept)
            if not is_hot_concept:
                continue
            
            '''
            buy_signal_list = row["buy_signal_list"]
            # 从信号列表中提取类型字段进行比较
            signal_types = set()
            for signal in buy_signal_list:
                if isinstance(signal, dict) and "type" in signal:
                    signal_types.add(signal["type"])
            
            has_singal = set(zb_buy_dict.keys()) & signal_types
            #print("has_singal",symbol, has_singal)
            if not has_singal:
                continue
            '''
            
            msg = f"【{STRATEGY_TYPE}】策略买入：{symbol}({name}), 买信号：{buy_signal_list}"
            smgr.push_wx_msg(symbol=symbol, strategy_type=STRATEGY_TYPE, msg_type=WxMsgType.TOPRANK_STRATEGY, msg=msg)   
            
            if is_debug is not None:
                print("满足条件的股票：",stock_df["symbol"].values[0], stock_df["name"].values[0], \
                    stock_df["changePercent"].values[0], stock_df["boardName"].values[0])
            else:
                return stock_df["symbol"].values[0], stock_df["name"].values[0], \
                    stock_df["changePercent"].values[0]

    yfyLog.logger.info(f"没有可买的【{STRATEGY_TYPE}】股票")
    return "0", f"没有可买的【{STRATEGY_TYPE}】股票", 100


def get_fit_time() -> bool:
    return True


def buy(symbol, name, chg_pct, is_debug = False) -> bool:
    buy_data_list = sconfig.get_strategy_buy_data(strategy_type=STRATEGY_TYPE, symbol=symbol)
    has_buy = smgr.buy_stock(symbol=symbol, pct_chg=chg_pct, buy_data_list=buy_data_list,
                             msg=f"【{STRATEGY_TYPE}】策略买入：" + name + ",涨跌幅：" + str(chg_pct),
                             strategy_type=STRATEGY_TYPE, is_debug=is_debug,up_price=0)

    return has_buy

if __name__ == "__main__":
    run("222")
    #buy("688246", "嘉和美康", 10, is_debug=True)
    #get_fit_time()
    #get_fangliang_condition("600391")
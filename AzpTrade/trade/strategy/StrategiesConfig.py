import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

import trade.network.YFYDataMgr as yfydm
import trade.log.YfyLog as yfylog
import trade.network.YFYAccountMgr as yfyam
import os
import json
from trade.manager.StockQualityScoreMgr import StockQualityScoreMgr
from trade.redis.StockRedisMgr import StockRedisMgr, RedisKeyPrefix

# <-----------------------------策略数据------------------------------------->
BUY_SINGAL_STRATEGY = "买信号"
EMA_BUY_SINGAL_STRATEGY = "趋势票买信号"
TOP_BUY_SINGAL_STRATEGY = "TOP买信号"
HYZL_BUY_SINGAL_STRATEGY = "HYZL买信号"
BUY_SINGAL_STRATEGY_3 = "买信号3"
BUY_SINGAL_SCORE_STRATEGY = "买信号分买"
FIVEMIN_SINGAL_STRATEGY = "5分钟K"
QUSHI_STRATEGY = "趋势"
PAIMING_STRATEGY = "排名股池"
RANKRATIO_STRATEGY = "排名变化"
TOPPOS_STRATEGY = "Top前排票"
TOPZHUJING_STRATEGY = "Top主净"
ZHUJING_STRATEGY = "主净"
RANKONE_STRATEGY = "排名第一"
XIAOSHIZHI_STRATEGY = "小市值"
XIAOGAOPOOL_60_STRATEGY = "新高股池60日"
XIAOGAOPOOL_120_STRATEGY = "新高股池120日"
XIAOGAOPOOL_250_STRATEGY = "新高股池250日"
BREAKEMA_STRATEGY = "均线突破"
RANKONETEST_STRATEGY = "排名第一Test"
FIRST10ZTGC_STRATEGY = "打10cmGC首板"
FIRST10ZTGC_T_STRATEGY = "打10cmGC首板Test"
FIRST10ZTGC_T200_STRATEGY = "打10cmGC前200首板"
FIRST10ZT_ALL_STRATEGY = "打10cm全场首板"
XIAOGAO_120ZT_STRATEGY = "打120日新高涨停"
FIRST10ZTGC2B_STRATEGY = "打10cmGC二板"
FIRST20ZTGC_STRATEGY = "打20cmGC首板"
FIRST10ZT_STRATEGY = "打10cm首板"
FIRST10ZT2B_STRATEGY = "打10cm二板"
FIRST10ZT5B_STRATEGY = "打10cm五板"
ENV10ZT_STRATEGY = "打Env10cm涨停"
FIRST20ZT_STRATEGY = "打20cm首板"
FIRST10ZT_TEST_STRATEGY = "打10cm首板Test"
FIRST10ZTGC_TEST_STRATEGY = "打10cmGC首板T"
BREAKRESIST_FT10ZT_STRATEGY = "破压力位10cm首板"
NEWSINFOTEST_STRATEGY = "新闻资讯"
TOP100_STRATEGY = "Top100排名"
KPLTCK_STRATEGY = "kpl题材库"
THXREDU_STRATEGY = "thx热度榜"
ZT3D_STRATEGY = "3日涨停"
LATE_SESSION_STRATEGY = "尾盘多头"
SHORT_LATE_SESSION_STRATEGY = "短线尾盘"
ZHUJINGRATIO_STRATEGY = "主净排名"
TOPPAIMING_STRATEGY = "Top排名"
LIANBAN10ZT_STRATEGY = "打10cm连板"
LIANBAN20ZT_STRATEGY = "打20cm连板"
T0ETF_STRATEGY = "T0ETF"
STAND_SUPPORT_STRATEGY = "站稳支撑位"
MULTITOPRANK_STRATEGY = "多排名前排"
MULTITOPRANK5MIN_STRATEGY = "多排名5Min前排"
TOPCONCEPT_STRATEGY = "概念前排"

MULTI_TRIGGER_STRATEGY = "多策略共振"
# -----------------------------------------------#
USER_SOFTWARE_BUY = "交易软件买"
EMA_AUTO_BUY = "均线自动"
PCT_AUTO_BUY = "涨跌自动"


def get_buy_data(account_type, cash_pct=0.05, limit_pct_chg=None, buy_amount=0, limit_max_pct_chg=2, max_buy_cnt=2,
                 symbol_types="00,60,30", is_simulate=True, cancel_time=60,is_dyn_pos=True):
    if limit_pct_chg is None:
        limit_pct_chg = [2, 4]
    buy_data = {
        "account_type": account_type,
        "cash_pct": cash_pct,
        "limit_pct_chg": limit_pct_chg,
        "buy_amount": buy_amount,
        "limit_max_pct_chg": limit_max_pct_chg,
        "max_buy_cnt": max_buy_cnt,
        "symbol_types": symbol_types,
        "dyn_pos":is_dyn_pos,  # 动态仓位比
        "is_simulate": is_simulate,
        "cancel_time": cancel_time
    }
    return buy_data


def get_strategy_buy_data(strategy_type: str = "", symbol: str = "", use_redis: bool = True):
    """
    获取策略买入配置数据，支持Redis优先级和fallback机制
    
    Args:
        strategy_type: 策略类型
        symbol: 股票代码（可选）
        use_redis: 是否优先使用Redis配置，默认True
        
    Returns:
        list: 买入配置数据列表
    """
    
    buy_data_feng, buy_data_nong, buy_data_tuo, buy_data_xiaoqi, buy_data_zqi = None, None, None, None, None
    buy_data_bin, buy_data_jun, buy_data_hai, buy_data_kai, buy_data_li = None, None, None, None, None
    buy_data_hua, buy_data_xiang, buy_data_zhu, buy_data_kun = None, None, None, None
    buy_data_bing,buy_data_zheng, buy_data_hong, buy_data_lan = None, None, None, None
    buy_data_yan, buy_data_fang, buy_data_can, buy_data_ming = None, None, None, None
    buy_data_ding, buy_data_lele, buy_data_hu = None, None, None


    if strategy_type == HYZL_BUY_SINGAL_STRATEGY:  # 买信号  3
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == STAND_SUPPORT_STRATEGY:  # 买信号  3
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == BUY_SINGAL_STRATEGY_3:  # 买信号  3
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == FIVEMIN_SINGAL_STRATEGY:  # 五分钟
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == BUY_SINGAL_SCORE_STRATEGY:  # 买信号分买
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == PAIMING_STRATEGY:  # 排名
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == RANKRATIO_STRATEGY:  # 排名变化
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == TOPPOS_STRATEGY:  # 头部变化票
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == TOPZHUJING_STRATEGY:  # 头部主净票
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == ZHUJING_STRATEGY:  # 主净
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == XIAOSHIZHI_STRATEGY:  # 小市值
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == BREAKEMA_STRATEGY:  # 均线突破
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == RANKONETEST_STRATEGY:  # 排名第一票测试
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[30, 40], cash_pct=0.03)

    if strategy_type == NEWSINFOTEST_STRATEGY:  # 新闻资讯
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == TOPCONCEPT_STRATEGY:  # 概念前排
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == TOP100_STRATEGY:  # top100
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[1, 2], cash_pct=0.1, limit_max_pct_chg=1, symbol_types="00,60", max_buy_cnt=1, is_simulate=True)

    if strategy_type == QUSHI_STRATEGY:  # 趋势
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == LATE_SESSION_STRATEGY:  # 尾盘买
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == ZHUJINGRATIO_STRATEGY:  # 主净排名变化
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == TOPPAIMING_STRATEGY:  # Top排名
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == ZT3D_STRATEGY:  # 3日内涨停过的票
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == MULTITOPRANK_STRATEGY:  # 3日内涨停过的票
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[4, 6], cash_pct=0.1, limit_max_pct_chg=3, max_buy_cnt=3, is_simulate=True)

    if strategy_type == MULTITOPRANK5MIN_STRATEGY:  # 3日内涨停过的票
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[4, 6], cash_pct=0.1, limit_max_pct_chg=3, max_buy_cnt=3, is_simulate=True)

    if strategy_type == MULTI_TRIGGER_STRATEGY:
        buy_data_xiaoqi = get_buy_data(account_type=yfyam.XIAOQI, cash_pct=0.1, symbol_types="00,60", max_buy_cnt=1, is_simulate=True)

    if strategy_type == THXREDU_STRATEGY:  # 同花顺热度榜
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[2, 3], cash_pct=0.15, limit_max_pct_chg=1, symbol_types="00,60", max_buy_cnt=1, is_simulate=True)

    if strategy_type == KPLTCK_STRATEGY:  # kpl题材库
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[1, 2], cash_pct=0.1, limit_max_pct_chg=2, symbol_types="00,60", max_buy_cnt=1, is_simulate=True)

    if strategy_type == RANKONE_STRATEGY:  # 第一名
        # buy_data_tuo = get_buy_data(account_type=yfyam.TUO, limit_pct_chg=[20, 40],
        #                            symbol_types="00,60", is_simulate=True)
        buy_data_xiaoqi = get_buy_data(account_type=yfyam.XIAOQI, limit_pct_chg=[20, 40],symbol_types="00,60", is_simulate=True)
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)

    if strategy_type == FIRST10ZT_STRATEGY:  # 10cm首板
        buy_data_xiaoqi = get_buy_data(account_type=yfyam.XIAOQI, limit_pct_chg=[50, 100], buy_amount=10000,max_buy_cnt=2, is_simulate=True)
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[50, 100], buy_amount=40000,max_buy_cnt=2, is_simulate=True)
    if strategy_type == FIRST10ZT2B_STRATEGY:  # 10cm二板
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[50, 100], buy_amount=30000,max_buy_cnt=1, is_simulate=True)

    if strategy_type == FIRST10ZT5B_STRATEGY:  # 10cm五板
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=1, is_simulate=True)
        buy_data_hai = get_buy_data(account_type=yfyam.HAI, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=1, is_simulate=True)

    if strategy_type == FIRST20ZT_STRATEGY:  # 20cm首板
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[100, 100], buy_amount=50000,max_buy_cnt=1, is_simulate=True)
    
    if strategy_type == BREAKRESIST_FT10ZT_STRATEGY:  # 破压力位10cm首板
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[50, 100],buy_amount=15000, max_buy_cnt=1, is_simulate=True)

    if strategy_type == ENV10ZT_STRATEGY:  # 10cm首板
        buy_data_xiaoqi = get_buy_data(account_type=yfyam.XIAOQI, limit_pct_chg=[50, 100], buy_amount=10000,max_buy_cnt=1, is_simulate=True)

    if strategy_type == LIANBAN10ZT_STRATEGY:  # 10cm连板
        buy_data_xiaoqi = get_buy_data(account_type=yfyam.XIAOQI, limit_pct_chg=[50, 100], buy_amount=5000,max_buy_cnt=1, is_simulate=True)

    if strategy_type == LIANBAN20ZT_STRATEGY:  # 20cm连板
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2)
    
    if strategy_type == FIRST10ZTGC_T_STRATEGY:
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[50, 100], cash_pct=0.1,max_buy_cnt=2, is_simulate=True)
    
    if strategy_type == SHORT_LATE_SESSION_STRATEGY:  # 短线黄昏选股
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[100, 100], buy_amount=15000,max_buy_cnt=3, is_simulate=True)
        buy_data_hai = get_buy_data(account_type=yfyam.HAI, limit_pct_chg=[100, 100], buy_amount=40000,max_buy_cnt=3, is_simulate=True)

    ####----------------------------------------实盘----------------------------------------###
    if strategy_type == BUY_SINGAL_STRATEGY:  # 买信号
        buy_data_li = get_buy_data(account_type=yfyam.LI, limit_pct_chg=[10, 12], cash_pct=0.03,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        #buy_data_hua = get_buy_data(account_type=yfyam.HUA, limit_pct_chg=[10, 12], cash_pct=0.03,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        #buy_data_xiang = get_buy_data(account_type=yfyam.XIANG, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=10, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_zhu = get_buy_data(account_type=yfyam.ZHU, limit_pct_chg=[10, 12], cash_pct=0.03, max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_kun = get_buy_data(account_type=yfyam.KUN, limit_pct_chg=[10, 12], cash_pct=0.03, max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        #buy_data_hong = get_buy_data(account_type=yfyam.HONG, limit_pct_chg=[10, 12], cash_pct=0.1,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_lan = get_buy_data(account_type=yfyam.LAN, limit_pct_chg=[10, 12], cash_pct=0.03,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_yan = get_buy_data(account_type=yfyam.YAN, limit_pct_chg=[10, 12], cash_pct=0.03,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_fang = get_buy_data(account_type=yfyam.FANG, limit_pct_chg=[10, 12], cash_pct=0.03,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")

        buy_data_hu = get_buy_data(account_type=yfyam.HU, limit_pct_chg=[10, 12], cash_pct=0.03,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60") 
        buy_data_lele = get_buy_data(account_type=yfyam.LELE, limit_pct_chg=[10, 12], cash_pct=0.03,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60") 
        buy_data_ding = get_buy_data(account_type=yfyam.DING, limit_pct_chg=[10, 12], cash_pct=0.03,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68") 
        #buy_data_bing = get_buy_data(account_type=yfyam.BING, limit_pct_chg=[10, 12], cash_pct=0.1,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[10, 12], cash_pct=0.03,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        #buy_data_bin = get_buy_data(account_type=yfyam.BIN, limit_pct_chg=[10, 12], cash_pct=0.1,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_hai = get_buy_data(account_type=yfyam.HAI, limit_pct_chg=[10, 12], cash_pct=0.03,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        #buy_data_kai = get_buy_data(account_type=yfyam.KAI, limit_pct_chg=[10, 12], cash_pct=0.1,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_can = get_buy_data(account_type=yfyam.CAN, limit_pct_chg=[10, 12], cash_pct=0.03,max_buy_cnt=20, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        #buy_data_ming = get_buy_data(account_type=yfyam.MING, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=10, is_simulate=False)

    if strategy_type == EMA_BUY_SINGAL_STRATEGY:  # 趋势票买信号
        '''
        buy_data_li = get_buy_data(account_type=yfyam.LI, limit_pct_chg=[7, 9], cash_pct=0.1,max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_zhu = get_buy_data(account_type=yfyam.ZHU, limit_pct_chg=[7, 9], cash_pct=0.1, max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30")
        buy_data_kun = get_buy_data(account_type=yfyam.KUN, limit_pct_chg=[7, 9], cash_pct=0.1, max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_lan = get_buy_data(account_type=yfyam.LAN, limit_pct_chg=[7, 9], cash_pct=0.1,max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_yan = get_buy_data(account_type=yfyam.YAN, limit_pct_chg=[7, 9], cash_pct=0.1,max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_fang = get_buy_data(account_type=yfyam.FANG, limit_pct_chg=[7, 9], cash_pct=0.1,max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30")

        buy_data_hu = get_buy_data(account_type=yfyam.HU, limit_pct_chg=[7, 9], cash_pct=0.1,max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60") 
        buy_data_lele = get_buy_data(account_type=yfyam.LELE, limit_pct_chg=[7, 9], cash_pct=0.1,max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60") 
        buy_data_ding = get_buy_data(account_type=yfyam.DING, limit_pct_chg=[7, 9], cash_pct=0.1,max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68") 
        buy_data_can = get_buy_data(account_type=yfyam.CAN, limit_pct_chg=[7, 9], cash_pct=0.1,max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        '''
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[7, 9], cash_pct=0.03,max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_hai = get_buy_data(account_type=yfyam.HAI, limit_pct_chg=[7, 9], cash_pct=0.02,max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")

    if strategy_type == TOP_BUY_SINGAL_STRATEGY:
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[7, 9], cash_pct=0.01,max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_hai = get_buy_data(account_type=yfyam.HAI, limit_pct_chg=[7, 9], cash_pct=0.01,max_buy_cnt=5, is_simulate=False, cancel_time=600,is_dyn_pos=False,symbol_types="00,60,30,68")

    if strategy_type == FIRST10ZTGC_STRATEGY:  # 10cm股池首板
        #buy_data_li = get_buy_data(account_type=yfyam.LI, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False, cancel_time=7200)
        #buy_data_hua = get_buy_data(account_type=yfyam.HUA, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #buy_data_xiang = get_buy_data(account_type=yfyam.XIANG, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #buy_data_zhu = get_buy_data(account_type=yfyam.ZHU, limit_pct_chg=[50, 100], cash_pct=0.05, max_buy_cnt=2, is_simulate=False)
        #buy_data_kun = get_buy_data(account_type=yfyam.KUN, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #buy_data_hong = get_buy_data(account_type=yfyam.HONG, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #buy_data_lan = get_buy_data(account_type=yfyam.LAN, limit_pct_chg=[50, 100], cash_pct=0.1,max_buy_cnt=2, is_simulate=False)
        #buy_data_yan = get_buy_data(account_type=yfyam.YAN, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False, cancel_time=7200)
        #buy_data_fang = get_buy_data(account_type=yfyam.FANG, limit_pct_chg=[50, 100], cash_pct=0.1,max_buy_cnt=2, is_simulate=False)

        #buy_data_bing = get_buy_data(account_type=yfyam.BING, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False, cancel_time=7200)
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[50, 100], cash_pct=0.03,max_buy_cnt=2, is_simulate=False, cancel_time=7200)
        #buy_data_bin = get_buy_data(account_type=yfyam.BIN, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #buy_data_hai = get_buy_data(account_type=yfyam.HAI, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #buy_data_kai = get_buy_data(account_type=yfyam.KAI, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False, cancel_time=7200)
        #buy_data_can = get_buy_data(account_type=yfyam.CAN, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False, cancel_time=7200)
        #buy_data_ming = get_buy_data(account_type=yfyam.MING, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False, cancel_time=7200)
    

    if strategy_type == FIRST10ZTGC2B_STRATEGY:  # # 10cm股池二板
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=1, is_simulate=False,cancel_time=7200)
        #buy_data_hai = get_buy_data(account_type=yfyam.HAI, limit_pct_chg=[50, 100], cash_pct=0.1,max_buy_cnt=1, is_simulate=False)

    if strategy_type == FIRST10ZT_ALL_STRATEGY: # 全场10cm首板
        #buy_data_li = get_buy_data(account_type=yfyam.LI, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False,cancel_time=4800)   
        #buy_data_hua = get_buy_data(account_type=yfyam.HUA, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #buy_data_xiang = get_buy_data(account_type=yfyam.XIANG, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #buy_data_zhu = get_buy_data(account_type=yfyam.ZHU, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #buy_data_kun = get_buy_data(account_type=yfyam.KUN, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #buy_data_hong = get_buy_data(account_type=yfyam.HONG, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #buy_data_lan = get_buy_data(account_type=yfyam.LAN, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #buy_data_yan = get_buy_data(account_type=yfyam.YAN, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False,cancel_time=4800)   
        #buy_data_fang = get_buy_data(account_type=yfyam.FANG, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #
        #buy_data_kai = get_buy_data(account_type=yfyam.KAI, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False,cancel_time=4800)
        #buy_data_hai = get_buy_data(account_type=yfyam.HAI, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        buy_data_feng=get_buy_data(account_type=yfyam.FENG_2,limit_pct_chg=[50, 100],cash_pct=0.01,max_buy_cnt=2, is_simulate=True,cancel_time=4800)
        #buy_data_bin = get_buy_data(account_type=yfyam.BIN, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False)
        #buy_data_can = get_buy_data(account_type=yfyam.CAN, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False,cancel_time=4800)   
        #buy_data_ming = get_buy_data(account_type=yfyam.MING, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False,cancel_time=4800)
        #buy_data_bing = get_buy_data(account_type=yfyam.BING, limit_pct_chg=[50, 100], cash_pct=0.05,max_buy_cnt=2, is_simulate=False,cancel_time=4800)

    
    if strategy_type == FIRST20ZTGC_STRATEGY:  # 20cm股池首板
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[100, 100], buy_amount=10000,max_buy_cnt=1, is_simulate=False,cancel_time=3600)
    
    if strategy_type == XIAOGAO_120ZT_STRATEGY:  # 120cm新高股池首板(目前主要是想打板)
        buy_data_li = get_buy_data(account_type=yfyam.LI, limit_pct_chg=[100, 100], cash_pct=0.01,max_buy_cnt=3, is_simulate=False, cancel_time=600,is_dyn_pos=False)
        buy_data_zhu = get_buy_data(account_type=yfyam.ZHU, limit_pct_chg=[100, 100], cash_pct=0.01, max_buy_cnt=3, is_simulate=False, cancel_time=600,is_dyn_pos=False)
        buy_data_kun = get_buy_data(account_type=yfyam.KUN, limit_pct_chg=[100, 100], cash_pct=0.01, max_buy_cnt=3, is_simulate=False, cancel_time=600,is_dyn_pos=False)
        buy_data_lan = get_buy_data(account_type=yfyam.LAN, limit_pct_chg=[100, 100], cash_pct=0.01,max_buy_cnt=3, is_simulate=False, cancel_time=600,is_dyn_pos=False)
        buy_data_yan = get_buy_data(account_type=yfyam.YAN, limit_pct_chg=[100, 100], cash_pct=0.01,max_buy_cnt=3, is_simulate=False, cancel_time=600,is_dyn_pos=False)
        buy_data_fang = get_buy_data(account_type=yfyam.FANG, limit_pct_chg=[100, 100], cash_pct=0.01,max_buy_cnt=3, is_simulate=False, cancel_time=600,is_dyn_pos=False)

        buy_data_hu = get_buy_data(account_type=yfyam.HU, limit_pct_chg=[100, 100], cash_pct=0.01,max_buy_cnt=3, is_simulate=False, cancel_time=600,is_dyn_pos=False) 
        buy_data_lele = get_buy_data(account_type=yfyam.LELE, limit_pct_chg=[100, 100], cash_pct=0.01,max_buy_cnt=3, is_simulate=False, cancel_time=600,is_dyn_pos=False) 
        buy_data_ding = get_buy_data(account_type=yfyam.DING, limit_pct_chg=[100, 100], cash_pct=0.01,max_buy_cnt=3, is_simulate=False, cancel_time=600,is_dyn_pos=False) 
        buy_data_can = get_buy_data(account_type=yfyam.CAN, limit_pct_chg=[100, 100], cash_pct=0.01,max_buy_cnt=3, is_simulate=False, cancel_time=600,is_dyn_pos=False)

        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2,limit_pct_chg=[100, 100], cash_pct=0.02,max_buy_cnt=3, is_simulate=False,cancel_time=600,is_dyn_pos=False)
        buy_data_hai = get_buy_data(account_type=yfyam.HAI, limit_pct_chg=[100, 100], cash_pct=0.01,max_buy_cnt=3, is_simulate=False,cancel_time=600,is_dyn_pos=False)
    
    if strategy_type == XIAOGAOPOOL_60_STRATEGY:  # 新高股池60日
        #buy_data_li = get_buy_data(account_type=yfyam.LI, limit_pct_chg=[5, 7], cash_pct=0.01,max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30,68")
        #buy_data_zhu = get_buy_data(account_type=yfyam.ZHU, limit_pct_chg=[5, 7], cash_pct=0.01, max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30")
        #buy_data_kun = get_buy_data(account_type=yfyam.KUN, limit_pct_chg=[5, 7], cash_pct=0.01, max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30,68")
        #buy_data_lan = get_buy_data(account_type=yfyam.LAN, limit_pct_chg=[5, 7], cash_pct=0.01,max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30")
        #buy_data_yan = get_buy_data(account_type=yfyam.YAN, limit_pct_chg=[5, 7], cash_pct=0.01,max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30,68")
        #buy_data_fang = get_buy_data(account_type=yfyam.FANG, limit_pct_chg=[5, 7], cash_pct=0.01,max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30,68")

        #buy_data_hu = get_buy_data(account_type=yfyam.HU, limit_pct_chg=[5, 7], cash_pct=0.01,max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60")
        #buy_data_lele = get_buy_data(account_type=yfyam.LELE, limit_pct_chg=[5, 7], cash_pct=0.01,max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60") 
        #buy_data_ding = get_buy_data(account_type=yfyam.DING, limit_pct_chg=[5, 7], cash_pct=0.01,max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30") 
        #buy_data_can = get_buy_data(account_type=yfyam.CAN, limit_pct_chg=[5, 7], cash_pct=0.01,max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30")
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[10, 12], cash_pct=0.02,max_buy_cnt=2, is_simulate=False, cancel_time=180,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_hai = get_buy_data(account_type=yfyam.HAI, limit_pct_chg=[10, 12], cash_pct=0.02,max_buy_cnt=2, is_simulate=False, cancel_time=180,is_dyn_pos=False,symbol_types="00,60,30,68")

    if strategy_type == XIAOGAOPOOL_250_STRATEGY:  # 新高股池250日
        #buy_data_li = get_buy_data(account_type=yfyam.LI, limit_pct_chg=[3, 5], cash_pct=0.02,max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30,68")
        #buy_data_zhu = get_buy_data(account_type=yfyam.ZHU, limit_pct_chg=[3, 5], cash_pct=0.02, max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30")
        #buy_data_kun = get_buy_data(account_type=yfyam.KUN, limit_pct_chg=[3, 5], cash_pct=0.02, max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30,68")
        #buy_data_lan = get_buy_data(account_type=yfyam.LAN, limit_pct_chg=[3, 5], cash_pct=0.02,max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30")
        #buy_data_yan = get_buy_data(account_type=yfyam.YAN, limit_pct_chg=[3, 5], cash_pct=0.02,max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30,68")
        #buy_data_fang = get_buy_data(account_type=yfyam.FANG, limit_pct_chg=[3, 5], cash_pct=0.02,max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60,30,68")

        #buy_data_hu = get_buy_data(account_type=yfyam.HU, limit_pct_chg=[3, 5], cash_pct=0.01,max_buy_cnt=2, is_simulate=False, cancel_time=180,symbol_types="00,60")
        #buy_data_lele = get_buy_data(account_type=yfyam.LELE, limit_pct_chg=[3, 5], cash_pct=0.02,max_buy_cnt=2, is_simulate=False, cancel_time=180,is_dyn_pos=True,symbol_types="00,60") 
        #buy_data_can = get_buy_data(account_type=yfyam.CAN, limit_pct_chg=[3, 5], cash_pct=0.02,max_buy_cnt=2, is_simulate=False, cancel_time=180,is_dyn_pos=True,symbol_types="00,60,30")
        #buy_data_ding = get_buy_data(account_type=yfyam.DING, limit_pct_chg=[3, 5], cash_pct=0.02,max_buy_cnt=2, is_simulate=False, cancel_time=180,is_dyn_pos=True,symbol_types="00,60,30") 
        buy_data_feng = get_buy_data(account_type=yfyam.FENG_2, limit_pct_chg=[4, 6], cash_pct=0.02,max_buy_cnt=2, is_simulate=False, cancel_time=180,is_dyn_pos=False,symbol_types="00,60,30,68")
        buy_data_hai = get_buy_data(account_type=yfyam.HAI, limit_pct_chg=[4, 6], cash_pct=0.02,max_buy_cnt=2, is_simulate=False, cancel_time=180,is_dyn_pos=False,symbol_types="00,60,30,68")
    
####----------------------------------------实盘----------------------------------------###

    limit_pct_chg_ratio = 0
    limit_3dPct_chg_ratio = 1
    symbol_simple_info = yfydm.get_stock_simple_info(symbol)
    if symbol != "" and symbol_simple_info is not None and not symbol_simple_info.empty:
        yesterdayRank = symbol_simple_info['yesterdayRank'].values[0]
        rank = symbol_simple_info['rank'].values[0]
        if yesterdayRank > 300:
            limit_pct_chg_ratio = max((yesterdayRank - rank) / 500, yesterdayRank / rank)
        else:
            limit_pct_chg_ratio = max((yesterdayRank - rank) / 50, yesterdayRank / rank)

        limit_3dPct_chg_ratio = min(1, 1 - symbol_simple_info["changePercent3D"].values[0] / 100)

    buy_data_list = [ #buy_data_bing,buy_data_xiaoqi,buy_data_bin,
                     buy_data_feng, buy_data_hai,
                     buy_data_li, #buy_data_hua, #buy_data_kai,
                     buy_data_zhu, buy_data_kun,#buy_data_xiang, 
                     buy_data_lan,buy_data_hu, # buy_data_zheng,buy_data_hong,
                     buy_data_yan, buy_data_fang, buy_data_can,
                     buy_data_ding,buy_data_lele #buy_data_ming,
                     ]
    buy_data_list = [b_data for b_data in buy_data_list if b_data is not None]
    b_data_list = []
    for b_data in buy_data_list:
        symbol_types_list = b_data["symbol_types"].split(",")
        if symbol[:2] in symbol_types_list:
            b_data_list.append(b_data)
    buy_data_list = b_data_list

    all_stock_df = yfydm.all_stock_info_df
    all_count = len(all_stock_df)
    count_greater_than_zero = all_stock_df["changePercent"].gt(0).sum()
    count_equal_to_zero = all_stock_df["changePercent"].eq(0).sum()
    rise_pct = float((count_greater_than_zero + count_equal_to_zero) / all_count)
    rise_pct = max(rise_pct, 0.65)
    # 根据连板数，动态调整一下投入金额；

    # 创建评分管理器实例
    score_mgr = StockQualityScoreMgr()
    # 获取某只股票的质量评分
    total_score, details = score_mgr.get_stock_quality_score(symbol)
    calculate_position = score_mgr.calculate_position(total_score)
    yfylog.logger.info(f"股票{symbol}动态仓位计算 - 质量分: {total_score:.2f}, 仓位比例: {calculate_position:.3f}")
    jggc_stocks = get_jggc_stocks()

    for data in buy_data_list:
        if limit_pct_chg_ratio > 1:
            limit_pct_10cm = (data["limit_pct_chg"][0] + min(limit_pct_chg_ratio * 0.8,
                                                             data["limit_max_pct_chg"])) * rise_pct
            limit_pct_20cm = (data["limit_pct_chg"][1] + min(limit_pct_chg_ratio * 0.8,
                                                             data["limit_max_pct_chg"])) * rise_pct
            data["limit_pct_chg"] = [limit_pct_10cm, limit_pct_20cm]

        buy_amount = data["buy_amount"]
        if buy_amount > 0:
            lianbanCount = symbol_simple_info["lianbanCount"].values[0]
            buy_amount_pct = 1
            if lianbanCount > 2:
                buy_amount_pct = min(lianbanCount / 2, 4)
            data["buy_amount"] = buy_amount / buy_amount_pct

        #if symbol in jggc_stocks:
        #    data["buy_amount"] *= 1.25
        #    data["cash_pct"] *= 1.25
        #    yfylog.logger.info(f"【{symbol}】J哥股池精选，调整买入金额和现金占比")

        #if data["dyn_pos"]:
        #    # 应用质量分对仓位的影响
        #    data["buy_amount"] *= calculate_position
        #    data["cash_pct"] *= calculate_position
            
    return buy_data_list


# ---------------------#---------------------#---------------------#
last_modified_time = None
strategy_config = {}
current_file_path = os.path.abspath(__file__)
directory_path = os.path.dirname(current_file_path)
url = directory_path + "/strategy_config.json"


def get_strategy_config():
    global strategy_config, url
    p_json = json.loads(open(url, encoding="utf-8").read())
    strategy_config = p_json


def check_reload_json():
    # current_file_path = os.path.abspath(__file__)
    # directory_path = os.path.dirname(current_file_path)
    # print(current_file_path, directory_path)

    global last_modified_time, url
    # 获取文件的最后修改时间
    current_modified_time = os.path.getmtime(url)
    # 如果文件的修改时间与上次记录的不同，则重启程序
    if last_modified_time is None or current_modified_time != last_modified_time:
        last_modified_time = current_modified_time
        get_strategy_config()
        yfylog.logger.warning(f"{url},配置文件已更新😄")
    else:
        yfylog.logger.info("配置文件strategy_config.json没有改变")


def get_user_control_symbols():
    global strategy_config
    if strategy_config is None or "user_control_symbols" not in strategy_config:
        return []
    # print(strategy_config["user_control_symbols"])
    return strategy_config["user_control_symbols"]


def get_symbols_sell_strategy(symbol):
    global strategy_config
    if strategy_config is None or "symbols_sell_strategy" not in strategy_config:
        return ""
    if symbol not in strategy_config["symbols_sell_strategy"]:
        return ""
    # print(strategy_config["user_control_symbols"])
    return strategy_config["symbols_sell_strategy"][symbol]


# 账户状态缓存
_account_status_cache = {
    "data": {},  # {account_name: status}
    "last_update": 0,
    "cache_duration": 60  # 缓存60秒，账户状态变化较少
}

def get_account_status(account_name):  # open，close
    """
    获取账户状态（优化版本）
    优化策略：
    1. 使用本地缓存减少Redis访问
    2. 复用Redis连接实例
    3. 批量获取所有账户状态
    """
    import time

    global _account_status_cache, _redis_mgr_instance, strategy_config

    current_time = time.time()

    # 检查本地缓存是否有效
    if (account_name in _account_status_cache["data"] and
        current_time - _account_status_cache["last_update"] < _account_status_cache["cache_duration"]):
        return _account_status_cache["data"][account_name]

    try:
        # 复用Redis连接实例
        if _redis_mgr_instance is None:
            _redis_mgr_instance = StockRedisMgr()

        redis_key = _redis_mgr_instance.build_key(RedisKeyPrefix.ACCOUNT_CONFIG.value, "status")

        # 批量获取所有账户状态（更高效）
        all_status = _redis_mgr_instance.hgetall_value(redis_key)

        if all_status:
            # 更新整个缓存
            _account_status_cache["data"] = all_status
            _account_status_cache["last_update"] = current_time
            yfylog.logger.debug(f"从Redis批量获取账户状态并缓存: {len(all_status)}个账户")

            return all_status.get(account_name, "")

    except Exception as e:
        yfylog.logger.error(f"redis获取账户 {account_name} 状态时发生异常: {e}")
        # Redis异常时，重置连接实例
        _redis_mgr_instance = None

    # 降级到配置文件
    fallback_status = ""
    if strategy_config is not None and "account_status" in strategy_config:
        if account_name in strategy_config["account_status"]:
            fallback_status = strategy_config["account_status"][account_name]

    # 缓存降级结果
    _account_status_cache["data"][account_name] = fallback_status
    _account_status_cache["last_update"] = current_time

    return fallback_status

# 全局缓存变量
_position_cache = {
    "value": None,
    "last_update": 0,
    "cache_duration": 30  # 缓存30秒
}
_redis_mgr_instance = None

def get_position():
    """获取账户仓位（优化版本）

    优化策略：
    1. 使用本地缓存减少Redis访问
    2. 复用Redis连接实例
    3. 合理的缓存失效时间
    """
    import time

    global _position_cache, _redis_mgr_instance, strategy_config

    current_time = time.time()

    # 检查本地缓存是否有效
    if (_position_cache["value"] is not None and
        current_time - _position_cache["last_update"] < _position_cache["cache_duration"]):
        return _position_cache["value"]

    try:
        # 复用Redis连接实例
        if _redis_mgr_instance is None:
            _redis_mgr_instance = StockRedisMgr()

        redis_key = _redis_mgr_instance.build_key(RedisKeyPrefix.ACCOUNT_CONFIG.value, "position")
        position = _redis_mgr_instance.get_value(redis_key)

        if position is not None:
            position_float = float(position)
            # 更新缓存
            _position_cache["value"] = position_float
            _position_cache["last_update"] = current_time
            yfylog.logger.debug(f"从Redis获取账户仓位并缓存: {position_float}")
            return position_float

    except Exception as e:
        yfylog.logger.error(f"redis获取账户仓位时发生异常: {e}")
        # Redis异常时，重置连接实例
        _redis_mgr_instance = None

    # 降级到配置文件
    fallback_position = 0.85
    if strategy_config is not None and "position" in strategy_config:
        fallback_position = strategy_config["position"]

    # 缓存降级结果（较短时间）
    _position_cache["value"] = fallback_position
    _position_cache["last_update"] = current_time
    _position_cache["cache_duration"] = 10  # 降级时缓存时间更短

    return fallback_position

def clear_position_cache():
    """清除仓位缓存，强制下次获取时重新从Redis读取"""
    global _position_cache
    _position_cache["value"] = None
    _position_cache["last_update"] = 0
    yfylog.logger.info("已清除仓位缓存")

def get_jggc_stocks() -> list[str]:
    global strategy_config
    if strategy_config is None or "jggcjx_stocks" not in strategy_config:
        return []
    return strategy_config["jggcjx_stocks"]

check_reload_json()

#get_account_status("bing")

#s = get_position()
#print(s)